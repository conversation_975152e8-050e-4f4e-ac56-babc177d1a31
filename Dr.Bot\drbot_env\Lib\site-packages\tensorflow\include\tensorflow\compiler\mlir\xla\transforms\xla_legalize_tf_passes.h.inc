/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CONVERTMHLOQUANTTOINT
#define GEN_PASS_DECL_LEGALIZETF
#define GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#define GEN_PASS_DECL_LEGALIZETFMODULEPASS
#define GEN_PASS_DECL_LEGALIZETFNOFALLBACK
#define GEN_PASS_DECL_LEGALIZETFTYPESPASS
#define GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ConvertMHLOQuantToInt
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMHLOQUANTTOINT
#undef GEN_PASS_DECL_CONVERTMHLOQUANTTOINT
#endif // GEN_PASS_DECL_CONVERTMHLOQUANTTOINT
#ifdef GEN_PASS_DEF_CONVERTMHLOQUANTTOINT
namespace impl {

template <typename DerivedT>
class ConvertMHLOQuantToIntBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ConvertMHLOQuantToIntBase;

  ConvertMHLOQuantToIntBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMHLOQuantToIntBase(const ConvertMHLOQuantToIntBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-mhlo-quant-to-int");
  }
  ::llvm::StringRef getArgument() const override { return "convert-mhlo-quant-to-int"; }

  ::llvm::StringRef getDescription() const override { return "Convert from MHLO quantized ops to MHLO primitive ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMHLOQuantToInt");
  }
  ::llvm::StringRef getName() const override { return "ConvertMHLOQuantToInt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMHLOQuantToIntBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMHLOQUANTTOINT
#endif // GEN_PASS_DEF_CONVERTMHLOQUANTTOINT

//===----------------------------------------------------------------------===//
// LegalizeTF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETF
struct LegalizeTFOptions {
  bool allow_partial_conversion_ = false;
  bool legalize_chlo_ = true;
  bool use_tf2xla_fallback_ = false;
  std::string device_type_ = "INVALID_DEVICE_TYPE";
  bool prefer_tf2xla_ = false;
};
#undef GEN_PASS_DECL_LEGALIZETF
#endif // GEN_PASS_DECL_LEGALIZETF
#ifdef GEN_PASS_DEF_LEGALIZETF
namespace impl {

template <typename DerivedT>
class LegalizeTFBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LegalizeTFBase;

  LegalizeTFBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFBase(const LegalizeTFBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTF");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect, chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<quant::QuantizationDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFBase<DerivedT>)

  LegalizeTFBase(const LegalizeTFOptions &options) : LegalizeTFBase() {
    allow_partial_conversion_ = options.allow_partial_conversion_;
    legalize_chlo_ = options.legalize_chlo_;
    use_tf2xla_fallback_ = options.use_tf2xla_fallback_;
    device_type_ = options.device_type_;
    prefer_tf2xla_ = options.prefer_tf2xla_;
  }
protected:
  ::mlir::Pass::Option<bool> allow_partial_conversion_{*this, "allow-partial-conversion", ::llvm::cl::desc("Allow operations that can't be legalized."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> use_tf2xla_fallback_{*this, "use-tf2xla-fallback", ::llvm::cl::desc("Use TF2XLA fallback for legalization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Must be specified if use-tf2xla-fallback is true, otherwise not used"), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
  ::mlir::Pass::Option<bool> prefer_tf2xla_{*this, "prefer-tf2xla", ::llvm::cl::desc("Prioritize tf2xla fallback legalization over MLIR legalization patterns"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETF
#endif // GEN_PASS_DEF_LEGALIZETF

//===----------------------------------------------------------------------===//
// LegalizeTFCollective
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#undef GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#endif // GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#ifdef GEN_PASS_DEF_LEGALIZETFCOLLECTIVE
namespace impl {

template <typename DerivedT>
class LegalizeTFCollectiveBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFCollectiveBase;

  LegalizeTFCollectiveBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFCollectiveBase(const LegalizeTFCollectiveBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-collective");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-collective"; }

  ::llvm::StringRef getDescription() const override { return "Legalize TF/XLA collective ops (TensorFlow dialect) to the HLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFCollective");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFCollective"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mhlo::MhloDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFCollectiveBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETFCOLLECTIVE
#endif // GEN_PASS_DEF_LEGALIZETFCOLLECTIVE

//===----------------------------------------------------------------------===//
// LegalizeTFModulePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETFMODULEPASS
struct LegalizeTFModulePassOptions {
  std::string device_type_ = "INVALID_DEVICE_TYPE";
};
#undef GEN_PASS_DECL_LEGALIZETFMODULEPASS
#endif // GEN_PASS_DECL_LEGALIZETFMODULEPASS
#ifdef GEN_PASS_DEF_LEGALIZETFMODULEPASS
namespace impl {

template <typename DerivedT>
class LegalizeTFModulePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFModulePassBase;

  LegalizeTFModulePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFModulePassBase(const LegalizeTFModulePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-fallback-legalize-tf-module-pass");
  }
  ::llvm::StringRef getArgument() const override { return "xla-fallback-legalize-tf-module-pass"; }

  ::llvm::StringRef getDescription() const override { return "Legalize whitelisted Ops using TF2XLA fallback for ops that must also be able to create new functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFModulePass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFModulePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect, chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFModulePassBase<DerivedT>)

  LegalizeTFModulePassBase(const LegalizeTFModulePassOptions &options) : LegalizeTFModulePassBase() {
    device_type_ = options.device_type_;
  }
protected:
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Required."), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETFMODULEPASS
#endif // GEN_PASS_DEF_LEGALIZETFMODULEPASS

//===----------------------------------------------------------------------===//
// LegalizeTFNoFallback
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETFNOFALLBACK
struct LegalizeTFNoFallbackOptions {
  bool allow_partial_conversion_ = false;
};
#undef GEN_PASS_DECL_LEGALIZETFNOFALLBACK
#endif // GEN_PASS_DECL_LEGALIZETFNOFALLBACK
#ifdef GEN_PASS_DEF_LEGALIZETFNOFALLBACK
namespace impl {

template <typename DerivedT>
class LegalizeTFNoFallbackBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LegalizeTFNoFallbackBase;

  LegalizeTFNoFallbackBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFNoFallbackBase(const LegalizeTFNoFallbackBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-no-fallback");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-no-fallback"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFNoFallback");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFNoFallback"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFNoFallbackBase<DerivedT>)

  LegalizeTFNoFallbackBase(const LegalizeTFNoFallbackOptions &options) : LegalizeTFNoFallbackBase() {
    allow_partial_conversion_ = options.allow_partial_conversion_;
  }
protected:
  ::mlir::Pass::Option<bool> allow_partial_conversion_{*this, "allow-partial-conversion", ::llvm::cl::desc("Allow operations that can't be legalized."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETFNOFALLBACK
#endif // GEN_PASS_DEF_LEGALIZETFNOFALLBACK

//===----------------------------------------------------------------------===//
// LegalizeTfTypesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETFTYPESPASS
#undef GEN_PASS_DECL_LEGALIZETFTYPESPASS
#endif // GEN_PASS_DECL_LEGALIZETFTYPESPASS
#ifdef GEN_PASS_DEF_LEGALIZETFTYPESPASS
namespace impl {

template <typename DerivedT>
class LegalizeTfTypesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LegalizeTfTypesPassBase;

  LegalizeTfTypesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTfTypesPassBase(const LegalizeTfTypesPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-types");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-types"; }

  ::llvm::StringRef getDescription() const override { return "Replace TensorFlow types with types that are legal in the MHLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTfTypesPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTfTypesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTfTypesPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETFTYPESPASS
#endif // GEN_PASS_DEF_LEGALIZETFTYPESPASS

//===----------------------------------------------------------------------===//
// VerifyTFXLALegalization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
struct VerifyTFXLALegalizationOptions {
  bool legalize_chlo_ = true;
};
#undef GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
#endif // GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
#ifdef GEN_PASS_DEF_VERIFYTFXLALEGALIZATION
namespace impl {

template <typename DerivedT>
class VerifyTFXLALegalizationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyTFXLALegalizationBase;

  VerifyTFXLALegalizationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyTFXLALegalizationBase(const VerifyTFXLALegalizationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfxla-verify-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "tfxla-verify-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that all TF ops have been legalized to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyTFXLALegalization");
  }
  ::llvm::StringRef getName() const override { return "VerifyTFXLALegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyTFXLALegalizationBase<DerivedT>)

  VerifyTFXLALegalizationBase(const VerifyTFXLALegalizationOptions &options) : VerifyTFXLALegalizationBase() {
    legalize_chlo_ = options.legalize_chlo_;
  }
protected:
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VERIFYTFXLALEGALIZATION
#endif // GEN_PASS_DEF_VERIFYTFXLALEGALIZATION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertMHLOQuantToInt Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMHLOQuantToInt() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createConvertMHLOQuantToIntPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMHLOQuantToIntPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createConvertMHLOQuantToIntPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTF Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTFCollective Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFCollective() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateLegalizeTFCollectivePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFCollectivePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateLegalizeTFCollectivePass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTFModulePass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFModulePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFModulePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFModulePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFModulePass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTFNoFallback Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFNoFallback() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFNoFallbackPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFNoFallbackPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFNoFallbackPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTfTypesPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTfTypesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::mhlo::CreateLegalizeTfTypesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTfTypesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::mhlo::CreateLegalizeTfTypesPass();
  });
}

//===----------------------------------------------------------------------===//
// VerifyTFXLALegalization Registration
//===----------------------------------------------------------------------===//

inline void registerVerifyTFXLALegalization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateVerifyTFXLALegalizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVerifyTFXLALegalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateVerifyTFXLALegalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTf Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTfPasses() {
  registerConvertMHLOQuantToInt();
  registerLegalizeTF();
  registerLegalizeTFCollective();
  registerLegalizeTFModulePass();
  registerLegalizeTFNoFallback();
  registerLegalizeTfTypesPass();
  registerVerifyTFXLALegalization();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ConvertMHLOQuantToIntBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ConvertMHLOQuantToIntBase;

  ConvertMHLOQuantToIntBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMHLOQuantToIntBase(const ConvertMHLOQuantToIntBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-mhlo-quant-to-int");
  }
  ::llvm::StringRef getArgument() const override { return "convert-mhlo-quant-to-int"; }

  ::llvm::StringRef getDescription() const override { return "Convert from MHLO quantized ops to MHLO primitive ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMHLOQuantToInt");
  }
  ::llvm::StringRef getName() const override { return "ConvertMHLOQuantToInt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMHLOQuantToIntBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LegalizeTFBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LegalizeTFBase;

  LegalizeTFBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFBase(const LegalizeTFBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTF");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect, chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<quant::QuantizationDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> allow_partial_conversion_{*this, "allow-partial-conversion", ::llvm::cl::desc("Allow operations that can't be legalized."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> use_tf2xla_fallback_{*this, "use-tf2xla-fallback", ::llvm::cl::desc("Use TF2XLA fallback for legalization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Must be specified if use-tf2xla-fallback is true, otherwise not used"), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
  ::mlir::Pass::Option<bool> prefer_tf2xla_{*this, "prefer-tf2xla", ::llvm::cl::desc("Prioritize tf2xla fallback legalization over MLIR legalization patterns"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LegalizeTFCollectiveBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFCollectiveBase;

  LegalizeTFCollectiveBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFCollectiveBase(const LegalizeTFCollectiveBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-collective");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-collective"; }

  ::llvm::StringRef getDescription() const override { return "Legalize TF/XLA collective ops (TensorFlow dialect) to the HLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFCollective");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFCollective"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<mhlo::MhloDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFCollectiveBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LegalizeTFModulePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFModulePassBase;

  LegalizeTFModulePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFModulePassBase(const LegalizeTFModulePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-fallback-legalize-tf-module-pass");
  }
  ::llvm::StringRef getArgument() const override { return "xla-fallback-legalize-tf-module-pass"; }

  ::llvm::StringRef getDescription() const override { return "Legalize whitelisted Ops using TF2XLA fallback for ops that must also be able to create new functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFModulePass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFModulePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect, chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFModulePassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Required."), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
};

template <typename DerivedT>
class LegalizeTFNoFallbackBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LegalizeTFNoFallbackBase;

  LegalizeTFNoFallbackBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFNoFallbackBase(const LegalizeTFNoFallbackBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-no-fallback");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-no-fallback"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFNoFallback");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFNoFallback"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithDialect>();

  registry.insert<chlo::ChloDialect>();

  registry.insert<mhlo::MhloDialect>();

  registry.insert<shape::ShapeDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFNoFallbackBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> allow_partial_conversion_{*this, "allow-partial-conversion", ::llvm::cl::desc("Allow operations that can't be legalized."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LegalizeTfTypesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LegalizeTfTypesPassBase;

  LegalizeTfTypesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTfTypesPassBase(const LegalizeTfTypesPassBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-types");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-types"; }

  ::llvm::StringRef getDescription() const override { return "Replace TensorFlow types with types that are legal in the MHLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTfTypesPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTfTypesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTfTypesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VerifyTFXLALegalizationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyTFXLALegalizationBase;

  VerifyTFXLALegalizationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyTFXLALegalizationBase(const VerifyTFXLALegalizationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfxla-verify-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "tfxla-verify-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that all TF ops have been legalized to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyTFXLALegalization");
  }
  ::llvm::StringRef getName() const override { return "VerifyTFXLALegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyTFXLALegalizationBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
