/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS
#define GEN_PASS_DECL_DEDUPBOUNDINPUTBINDINGPASS
#define GEN_PASS_DECL_FREEZEASSETSPASS
#define GEN_PASS_DECL_FREEZEGLOBALTENSORSPASS
#define GEN_PASS_DECL_LOWERGLOBALSTOMLPROGRAMPASS
#define GEN_PASS_DECL_LOWERVARIABLEOPSTOMLPROGRAMPASS
#define GEN_PASS_DECL_OPTIMIZEGLOBALTENSORSPASS
#define GEN_PASS_DECL_REMOVEVARIABLESINSESSIONINITIALIZERPASS
#define GEN_PASS_DECL_STRIPSAVEDMODULEMETADATAPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ConvertSessionInitializerToFunctionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS
#undef GEN_PASS_DECL_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS
#endif // GEN_PASS_DECL_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS
#ifdef GEN_PASS_DEF_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS
namespace impl {

template <typename DerivedT>
class ConvertSessionInitializerToFunctionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSessionInitializerToFunctionPassBase;

  ConvertSessionInitializerToFunctionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSessionInitializerToFunctionPassBase(const ConvertSessionInitializerToFunctionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-convert-session-initializer-to-function");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-convert-session-initializer-to-function"; }

  ::llvm::StringRef getDescription() const override { return "Converts the session initializer to a function."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSessionInitializerToFunctionPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSessionInitializerToFunctionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSessionInitializerToFunctionPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS
#endif // GEN_PASS_DEF_CONVERTSESSIONINITIALIZERTOFUNCTIONPASS

//===----------------------------------------------------------------------===//
// DedupBoundInputBindingPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DEDUPBOUNDINPUTBINDINGPASS
#undef GEN_PASS_DECL_DEDUPBOUNDINPUTBINDINGPASS
#endif // GEN_PASS_DECL_DEDUPBOUNDINPUTBINDINGPASS
#ifdef GEN_PASS_DEF_DEDUPBOUNDINPUTBINDINGPASS
namespace impl {

template <typename DerivedT>
class DedupBoundInputBindingPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = DedupBoundInputBindingPassBase;

  DedupBoundInputBindingPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DedupBoundInputBindingPassBase(const DedupBoundInputBindingPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-dedup-bound-input-binding-pass");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-dedup-bound-input-binding-pass"; }

  ::llvm::StringRef getDescription() const override { return "Remove duplicate 'tf_saved_model.bound_input' bindings."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DedupBoundInputBindingPass");
  }
  ::llvm::StringRef getName() const override { return "DedupBoundInputBindingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DedupBoundInputBindingPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DEDUPBOUNDINPUTBINDINGPASS
#endif // GEN_PASS_DEF_DEDUPBOUNDINPUTBINDINGPASS

//===----------------------------------------------------------------------===//
// FreezeAssetsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FREEZEASSETSPASS
#undef GEN_PASS_DECL_FREEZEASSETSPASS
#endif // GEN_PASS_DECL_FREEZEASSETSPASS
#ifdef GEN_PASS_DEF_FREEZEASSETSPASS
namespace impl {

template <typename DerivedT>
class FreezeAssetsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FreezeAssetsPassBase;

  FreezeAssetsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FreezeAssetsPassBase(const FreezeAssetsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-freeze-assets");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-freeze-assets"; }

  ::llvm::StringRef getDescription() const override { return "Freeze tf_saved_model.asset's in func bodies."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FreezeAssetsPass");
  }
  ::llvm::StringRef getName() const override { return "FreezeAssetsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FreezeAssetsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FREEZEASSETSPASS
#endif // GEN_PASS_DEF_FREEZEASSETSPASS

//===----------------------------------------------------------------------===//
// FreezeGlobalTensorsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FREEZEGLOBALTENSORSPASS
struct FreezeGlobalTensorsPassOptions {
  bool allow_mutable_tensors = false;
};
#undef GEN_PASS_DECL_FREEZEGLOBALTENSORSPASS
#endif // GEN_PASS_DECL_FREEZEGLOBALTENSORSPASS
#ifdef GEN_PASS_DEF_FREEZEGLOBALTENSORSPASS
namespace impl {

template <typename DerivedT>
class FreezeGlobalTensorsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FreezeGlobalTensorsPassBase;

  FreezeGlobalTensorsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FreezeGlobalTensorsPassBase(const FreezeGlobalTensorsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-freeze-global-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-freeze-global-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Freeze tf_saved_model.global_tensor's in func bodies."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FreezeGlobalTensorsPass");
  }
  ::llvm::StringRef getName() const override { return "FreezeGlobalTensorsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FreezeGlobalTensorsPassBase<DerivedT>)

  FreezeGlobalTensorsPassBase(const FreezeGlobalTensorsPassOptions &options) : FreezeGlobalTensorsPassBase() {
    allow_mutable_tensors = options.allow_mutable_tensors;
  }
protected:
  ::mlir::Pass::Option<bool> allow_mutable_tensors{*this, "allow-mutable-tensors", ::llvm::cl::desc("Allows mutable tensors to be in the graph."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FREEZEGLOBALTENSORSPASS
#endif // GEN_PASS_DEF_FREEZEGLOBALTENSORSPASS

//===----------------------------------------------------------------------===//
// LowerGlobalsToMlProgramPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERGLOBALSTOMLPROGRAMPASS
#undef GEN_PASS_DECL_LOWERGLOBALSTOMLPROGRAMPASS
#endif // GEN_PASS_DECL_LOWERGLOBALSTOMLPROGRAMPASS
#ifdef GEN_PASS_DEF_LOWERGLOBALSTOMLPROGRAMPASS
namespace impl {

template <typename DerivedT>
class LowerGlobalsToMlProgramPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = LowerGlobalsToMlProgramPassBase;

  LowerGlobalsToMlProgramPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerGlobalsToMlProgramPassBase(const LowerGlobalsToMlProgramPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-lower-globals-to-mlprogram");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-lower-globals-to-mlprogram"; }

  ::llvm::StringRef getDescription() const override { return "Remove (and remap) function arguments that map to global tensors."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerGlobalsToMlProgramPass");
  }
  ::llvm::StringRef getName() const override { return "LowerGlobalsToMlProgramPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerGlobalsToMlProgramPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOWERGLOBALSTOMLPROGRAMPASS
#endif // GEN_PASS_DEF_LOWERGLOBALSTOMLPROGRAMPASS

//===----------------------------------------------------------------------===//
// LowerVariableOpsToMlProgramPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERVARIABLEOPSTOMLPROGRAMPASS
#undef GEN_PASS_DECL_LOWERVARIABLEOPSTOMLPROGRAMPASS
#endif // GEN_PASS_DECL_LOWERVARIABLEOPSTOMLPROGRAMPASS
#ifdef GEN_PASS_DEF_LOWERVARIABLEOPSTOMLPROGRAMPASS
namespace impl {

template <typename DerivedT>
class LowerVariableOpsToMlProgramPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = LowerVariableOpsToMlProgramPassBase;

  LowerVariableOpsToMlProgramPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVariableOpsToMlProgramPassBase(const LowerVariableOpsToMlProgramPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-lower-variable-ops-to-mlprogram");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-lower-variable-ops-to-mlprogram"; }

  ::llvm::StringRef getDescription() const override { return "Lower tf.ReadVariable and tf.AssignVariable"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVariableOpsToMlProgramPass");
  }
  ::llvm::StringRef getName() const override { return "LowerVariableOpsToMlProgramPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVariableOpsToMlProgramPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOWERVARIABLEOPSTOMLPROGRAMPASS
#endif // GEN_PASS_DEF_LOWERVARIABLEOPSTOMLPROGRAMPASS

//===----------------------------------------------------------------------===//
// OptimizeGlobalTensorsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_OPTIMIZEGLOBALTENSORSPASS
#undef GEN_PASS_DECL_OPTIMIZEGLOBALTENSORSPASS
#endif // GEN_PASS_DECL_OPTIMIZEGLOBALTENSORSPASS
#ifdef GEN_PASS_DEF_OPTIMIZEGLOBALTENSORSPASS
namespace impl {

template <typename DerivedT>
class OptimizeGlobalTensorsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OptimizeGlobalTensorsPassBase;

  OptimizeGlobalTensorsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeGlobalTensorsPassBase(const OptimizeGlobalTensorsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-optimize-global-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-optimize-global-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Optimize tf_saved_model.global_tensor's."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeGlobalTensorsPass");
  }
  ::llvm::StringRef getName() const override { return "OptimizeGlobalTensorsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OptimizeGlobalTensorsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_OPTIMIZEGLOBALTENSORSPASS
#endif // GEN_PASS_DEF_OPTIMIZEGLOBALTENSORSPASS

//===----------------------------------------------------------------------===//
// RemoveVariablesInSessionInitializerPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_REMOVEVARIABLESINSESSIONINITIALIZERPASS
#undef GEN_PASS_DECL_REMOVEVARIABLESINSESSIONINITIALIZERPASS
#endif // GEN_PASS_DECL_REMOVEVARIABLESINSESSIONINITIALIZERPASS
#ifdef GEN_PASS_DEF_REMOVEVARIABLESINSESSIONINITIALIZERPASS
namespace impl {

template <typename DerivedT>
class RemoveVariablesInSessionInitializerPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RemoveVariablesInSessionInitializerPassBase;

  RemoveVariablesInSessionInitializerPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveVariablesInSessionInitializerPassBase(const RemoveVariablesInSessionInitializerPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-remove-vars-in-session-initializer");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-remove-vars-in-session-initializer"; }

  ::llvm::StringRef getDescription() const override { return "Remove variables in tf saved model's session initializer."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveVariablesInSessionInitializerPass");
  }
  ::llvm::StringRef getName() const override { return "RemoveVariablesInSessionInitializerPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveVariablesInSessionInitializerPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_REMOVEVARIABLESINSESSIONINITIALIZERPASS
#endif // GEN_PASS_DEF_REMOVEVARIABLESINSESSIONINITIALIZERPASS

//===----------------------------------------------------------------------===//
// StripSavedModuleMetadataPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STRIPSAVEDMODULEMETADATAPASS
#undef GEN_PASS_DECL_STRIPSAVEDMODULEMETADATAPASS
#endif // GEN_PASS_DECL_STRIPSAVEDMODULEMETADATAPASS
#ifdef GEN_PASS_DEF_STRIPSAVEDMODULEMETADATAPASS
namespace impl {

template <typename DerivedT>
class StripSavedModuleMetadataPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StripSavedModuleMetadataPassBase;

  StripSavedModuleMetadataPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StripSavedModuleMetadataPassBase(const StripSavedModuleMetadataPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-strip-saved-module-metadata");
  }
  ::llvm::StringRef getArgument() const override { return "tf-strip-saved-module-metadata"; }

  ::llvm::StringRef getDescription() const override { return "Removes saved_model attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripSavedModuleMetadataPass");
  }
  ::llvm::StringRef getName() const override { return "StripSavedModuleMetadataPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripSavedModuleMetadataPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STRIPSAVEDMODULEMETADATAPASS
#endif // GEN_PASS_DEF_STRIPSAVEDMODULEMETADATAPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertSessionInitializerToFunctionPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSessionInitializerToFunctionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateConvertSessionInitializerToFunctionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertSessionInitializerToFunctionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateConvertSessionInitializerToFunctionPass();
  });
}

//===----------------------------------------------------------------------===//
// DedupBoundInputBindingPass Registration
//===----------------------------------------------------------------------===//

inline void registerDedupBoundInputBindingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateDedupBoundInputBindingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDedupBoundInputBindingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateDedupBoundInputBindingPass();
  });
}

//===----------------------------------------------------------------------===//
// FreezeAssetsPass Registration
//===----------------------------------------------------------------------===//

inline void registerFreezeAssetsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateFreezeAssetsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFreezeAssetsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateFreezeAssetsPass();
  });
}

//===----------------------------------------------------------------------===//
// FreezeGlobalTensorsPass Registration
//===----------------------------------------------------------------------===//

inline void registerFreezeGlobalTensorsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateFreezeGlobalTensorsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFreezeGlobalTensorsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateFreezeGlobalTensorsPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerGlobalsToMlProgramPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerGlobalsToMlProgramPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateLowerGlobalsToMlProgramPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerGlobalsToMlProgramPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateLowerGlobalsToMlProgramPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerVariableOpsToMlProgramPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerVariableOpsToMlProgramPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateLowerVariableOpsToMlProgramPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerVariableOpsToMlProgramPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateLowerVariableOpsToMlProgramPass();
  });
}

//===----------------------------------------------------------------------===//
// OptimizeGlobalTensorsPass Registration
//===----------------------------------------------------------------------===//

inline void registerOptimizeGlobalTensorsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateOptimizeGlobalTensorsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOptimizeGlobalTensorsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateOptimizeGlobalTensorsPass();
  });
}

//===----------------------------------------------------------------------===//
// RemoveVariablesInSessionInitializerPass Registration
//===----------------------------------------------------------------------===//

inline void registerRemoveVariablesInSessionInitializerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateRemoveVariablesInSessionInitializerPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerRemoveVariablesInSessionInitializerPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::tf_saved_model::CreateRemoveVariablesInSessionInitializerPass();
  });
}

//===----------------------------------------------------------------------===//
// StripSavedModuleMetadataPass Registration
//===----------------------------------------------------------------------===//

inline void registerStripSavedModuleMetadataPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateStripSavedModuleMetadataPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStripSavedModuleMetadataPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::tf_saved_model::CreateStripSavedModuleMetadataPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowSavedModel Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowSavedModelPasses() {
  registerConvertSessionInitializerToFunctionPass();
  registerDedupBoundInputBindingPass();
  registerFreezeAssetsPass();
  registerFreezeGlobalTensorsPass();
  registerLowerGlobalsToMlProgramPass();
  registerLowerVariableOpsToMlProgramPass();
  registerOptimizeGlobalTensorsPass();
  registerRemoveVariablesInSessionInitializerPass();
  registerStripSavedModuleMetadataPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ConvertSessionInitializerToFunctionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSessionInitializerToFunctionPassBase;

  ConvertSessionInitializerToFunctionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSessionInitializerToFunctionPassBase(const ConvertSessionInitializerToFunctionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-convert-session-initializer-to-function");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-convert-session-initializer-to-function"; }

  ::llvm::StringRef getDescription() const override { return "Converts the session initializer to a function."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSessionInitializerToFunctionPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSessionInitializerToFunctionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSessionInitializerToFunctionPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DedupBoundInputBindingPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = DedupBoundInputBindingPassBase;

  DedupBoundInputBindingPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DedupBoundInputBindingPassBase(const DedupBoundInputBindingPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-dedup-bound-input-binding-pass");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-dedup-bound-input-binding-pass"; }

  ::llvm::StringRef getDescription() const override { return "Remove duplicate 'tf_saved_model.bound_input' bindings."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DedupBoundInputBindingPass");
  }
  ::llvm::StringRef getName() const override { return "DedupBoundInputBindingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DedupBoundInputBindingPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FreezeAssetsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FreezeAssetsPassBase;

  FreezeAssetsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FreezeAssetsPassBase(const FreezeAssetsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-freeze-assets");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-freeze-assets"; }

  ::llvm::StringRef getDescription() const override { return "Freeze tf_saved_model.asset's in func bodies."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FreezeAssetsPass");
  }
  ::llvm::StringRef getName() const override { return "FreezeAssetsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FreezeAssetsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FreezeGlobalTensorsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FreezeGlobalTensorsPassBase;

  FreezeGlobalTensorsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FreezeGlobalTensorsPassBase(const FreezeGlobalTensorsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-freeze-global-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-freeze-global-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Freeze tf_saved_model.global_tensor's in func bodies."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FreezeGlobalTensorsPass");
  }
  ::llvm::StringRef getName() const override { return "FreezeGlobalTensorsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FreezeGlobalTensorsPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> allow_mutable_tensors{*this, "allow-mutable-tensors", ::llvm::cl::desc("Allows mutable tensors to be in the graph."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LowerGlobalsToMlProgramPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = LowerGlobalsToMlProgramPassBase;

  LowerGlobalsToMlProgramPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerGlobalsToMlProgramPassBase(const LowerGlobalsToMlProgramPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-lower-globals-to-mlprogram");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-lower-globals-to-mlprogram"; }

  ::llvm::StringRef getDescription() const override { return "Remove (and remap) function arguments that map to global tensors."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerGlobalsToMlProgramPass");
  }
  ::llvm::StringRef getName() const override { return "LowerGlobalsToMlProgramPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerGlobalsToMlProgramPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LowerVariableOpsToMlProgramPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = LowerVariableOpsToMlProgramPassBase;

  LowerVariableOpsToMlProgramPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVariableOpsToMlProgramPassBase(const LowerVariableOpsToMlProgramPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-lower-variable-ops-to-mlprogram");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-lower-variable-ops-to-mlprogram"; }

  ::llvm::StringRef getDescription() const override { return "Lower tf.ReadVariable and tf.AssignVariable"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVariableOpsToMlProgramPass");
  }
  ::llvm::StringRef getName() const override { return "LowerVariableOpsToMlProgramPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVariableOpsToMlProgramPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class OptimizeGlobalTensorsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OptimizeGlobalTensorsPassBase;

  OptimizeGlobalTensorsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OptimizeGlobalTensorsPassBase(const OptimizeGlobalTensorsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-optimize-global-tensors");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-optimize-global-tensors"; }

  ::llvm::StringRef getDescription() const override { return "Optimize tf_saved_model.global_tensor's."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OptimizeGlobalTensorsPass");
  }
  ::llvm::StringRef getName() const override { return "OptimizeGlobalTensorsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OptimizeGlobalTensorsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class RemoveVariablesInSessionInitializerPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RemoveVariablesInSessionInitializerPassBase;

  RemoveVariablesInSessionInitializerPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveVariablesInSessionInitializerPassBase(const RemoveVariablesInSessionInitializerPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-saved-model-remove-vars-in-session-initializer");
  }
  ::llvm::StringRef getArgument() const override { return "tf-saved-model-remove-vars-in-session-initializer"; }

  ::llvm::StringRef getDescription() const override { return "Remove variables in tf saved model's session initializer."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveVariablesInSessionInitializerPass");
  }
  ::llvm::StringRef getName() const override { return "RemoveVariablesInSessionInitializerPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveVariablesInSessionInitializerPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StripSavedModuleMetadataPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StripSavedModuleMetadataPassBase;

  StripSavedModuleMetadataPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StripSavedModuleMetadataPassBase(const StripSavedModuleMetadataPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-strip-saved-module-metadata");
  }
  ::llvm::StringRef getArgument() const override { return "tf-strip-saved-module-metadata"; }

  ::llvm::StringRef getDescription() const override { return "Removes saved_model attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripSavedModuleMetadataPass");
  }
  ::llvm::StringRef getName() const override { return "StripSavedModuleMetadataPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripSavedModuleMetadataPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
