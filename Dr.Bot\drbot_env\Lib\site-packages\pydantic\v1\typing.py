# ruff: noqa: F403,F401,F405
# flake8: noqa: F401,F405
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from pydantic.typing import *
else:
    # explicit importing
    from pydantic.typing import (
        Annotated,
        AnyArgTCallable,
        AnyCallable,
        Final,
        Literal,
        NoArgAnyCallable,
        NoneType,
        StrPath,
        WithArgsTypes,
        all_literal_values,
        display_as_type,
        get_all_type_hints,
        get_args,
        get_origin,
        get_sub_types,
        is_callable_type,
        is_classvar,
        is_finalvar,
        is_literal_type,
        is_namedtuple,
        is_new_type,
        is_none_type,
        is_typeddict,
        is_typeddict_special,
        is_union,
        new_type_supertype,
        resolve_annotations,
        typing_base,
        update_field_forward_refs,
        update_model_forward_refs,
    )

# mimick same __all__ as base typing module
__all__ = (
    'AnyCallable',
    'NoArgAnyCallable',
    'NoneType',
    'is_none_type',
    'display_as_type',
    'resolve_annotations',
    'is_callable_type',
    'is_literal_type',
    'all_literal_values',
    'is_namedtuple',
    'is_typeddict',
    'is_typeddict_special',
    'is_new_type',
    'new_type_supertype',
    'is_classvar',
    'is_finalvar',
    'update_field_forward_refs',
    'update_model_forward_refs',
    'TupleGenerator',
    'DictStrAny',
    'DictAny',
    'SetStr',
    'ListStr',
    'IntStr',
    'AbstractSetIntStr',
    'DictIntStrAny',
    'CallableGenerator',
    'ReprArgs',
    'AnyClassMethod',
    'CallableGenerator',
    'WithArgsTypes',
    'get_args',
    'get_origin',
    'get_sub_types',
    'typing_base',
    'get_all_type_hints',
    'is_union',
    'StrPath',
    'MappingIntStrAny',
)
