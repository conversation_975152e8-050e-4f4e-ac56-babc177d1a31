// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debugger_event_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto;
namespace third_party {
namespace tensorflow {
namespace core {
namespace debug {
class DebuggerEventMetadata;
struct DebuggerEventMetadataDefaultTypeInternal;
extern DebuggerEventMetadataDefaultTypeInternal _DebuggerEventMetadata_default_instance_;
}  // namespace debug
}  // namespace core
}  // namespace tensorflow
}  // namespace third_party
PROTOBUF_NAMESPACE_OPEN
template<> ::third_party::tensorflow::core::debug::DebuggerEventMetadata* Arena::CreateMaybeMessage<::third_party::tensorflow::core::debug::DebuggerEventMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace third_party {
namespace tensorflow {
namespace core {
namespace debug {

// ===================================================================

class DebuggerEventMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:third_party.tensorflow.core.debug.DebuggerEventMetadata) */ {
 public:
  inline DebuggerEventMetadata() : DebuggerEventMetadata(nullptr) {}
  ~DebuggerEventMetadata() override;
  explicit PROTOBUF_CONSTEXPR DebuggerEventMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebuggerEventMetadata(const DebuggerEventMetadata& from);
  DebuggerEventMetadata(DebuggerEventMetadata&& from) noexcept
    : DebuggerEventMetadata() {
    *this = ::std::move(from);
  }

  inline DebuggerEventMetadata& operator=(const DebuggerEventMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggerEventMetadata& operator=(DebuggerEventMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebuggerEventMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebuggerEventMetadata* internal_default_instance() {
    return reinterpret_cast<const DebuggerEventMetadata*>(
               &_DebuggerEventMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebuggerEventMetadata& a, DebuggerEventMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggerEventMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggerEventMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebuggerEventMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebuggerEventMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebuggerEventMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebuggerEventMetadata& from) {
    DebuggerEventMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggerEventMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "third_party.tensorflow.core.debug.DebuggerEventMetadata";
  }
  protected:
  explicit DebuggerEventMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 1,
    kOutputSlotFieldNumber = 2,
    kNumChunksFieldNumber = 3,
    kChunkIndexFieldNumber = 4,
  };
  // string device = 1;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // int32 output_slot = 2;
  void clear_output_slot();
  int32_t output_slot() const;
  void set_output_slot(int32_t value);
  private:
  int32_t _internal_output_slot() const;
  void _internal_set_output_slot(int32_t value);
  public:

  // int32 num_chunks = 3;
  void clear_num_chunks();
  int32_t num_chunks() const;
  void set_num_chunks(int32_t value);
  private:
  int32_t _internal_num_chunks() const;
  void _internal_set_num_chunks(int32_t value);
  public:

  // int32 chunk_index = 4;
  void clear_chunk_index();
  int32_t chunk_index() const;
  void set_chunk_index(int32_t value);
  private:
  int32_t _internal_chunk_index() const;
  void _internal_set_chunk_index(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:third_party.tensorflow.core.debug.DebuggerEventMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    int32_t output_slot_;
    int32_t num_chunks_;
    int32_t chunk_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebuggerEventMetadata

// string device = 1;
inline void DebuggerEventMetadata::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& DebuggerEventMetadata::device() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggerEventMetadata::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}
inline std::string* DebuggerEventMetadata::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return _s;
}
inline const std::string& DebuggerEventMetadata::_internal_device() const {
  return _impl_.device_.Get();
}
inline void DebuggerEventMetadata::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggerEventMetadata::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggerEventMetadata::release_device() {
  // @@protoc_insertion_point(field_release:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
  return _impl_.device_.Release();
}
inline void DebuggerEventMetadata::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:third_party.tensorflow.core.debug.DebuggerEventMetadata.device)
}

// int32 output_slot = 2;
inline void DebuggerEventMetadata::clear_output_slot() {
  _impl_.output_slot_ = 0;
}
inline int32_t DebuggerEventMetadata::_internal_output_slot() const {
  return _impl_.output_slot_;
}
inline int32_t DebuggerEventMetadata::output_slot() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.output_slot)
  return _internal_output_slot();
}
inline void DebuggerEventMetadata::_internal_set_output_slot(int32_t value) {
  
  _impl_.output_slot_ = value;
}
inline void DebuggerEventMetadata::set_output_slot(int32_t value) {
  _internal_set_output_slot(value);
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.output_slot)
}

// int32 num_chunks = 3;
inline void DebuggerEventMetadata::clear_num_chunks() {
  _impl_.num_chunks_ = 0;
}
inline int32_t DebuggerEventMetadata::_internal_num_chunks() const {
  return _impl_.num_chunks_;
}
inline int32_t DebuggerEventMetadata::num_chunks() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.num_chunks)
  return _internal_num_chunks();
}
inline void DebuggerEventMetadata::_internal_set_num_chunks(int32_t value) {
  
  _impl_.num_chunks_ = value;
}
inline void DebuggerEventMetadata::set_num_chunks(int32_t value) {
  _internal_set_num_chunks(value);
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.num_chunks)
}

// int32 chunk_index = 4;
inline void DebuggerEventMetadata::clear_chunk_index() {
  _impl_.chunk_index_ = 0;
}
inline int32_t DebuggerEventMetadata::_internal_chunk_index() const {
  return _impl_.chunk_index_;
}
inline int32_t DebuggerEventMetadata::chunk_index() const {
  // @@protoc_insertion_point(field_get:third_party.tensorflow.core.debug.DebuggerEventMetadata.chunk_index)
  return _internal_chunk_index();
}
inline void DebuggerEventMetadata::_internal_set_chunk_index(int32_t value) {
  
  _impl_.chunk_index_ = value;
}
inline void DebuggerEventMetadata::set_chunk_index(int32_t value) {
  _internal_set_chunk_index(value);
  // @@protoc_insertion_point(field_set:third_party.tensorflow.core.debug.DebuggerEventMetadata.chunk_index)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace debug
}  // namespace core
}  // namespace tensorflow
}  // namespace third_party

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebugger_5fevent_5fmetadata_2eproto
