// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/feature.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fexample_2ffeature_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fexample_2ffeature_2eproto;
namespace tensorflow {
class BytesList;
struct BytesListDefaultTypeInternal;
extern BytesListDefaultTypeInternal _BytesList_default_instance_;
class Feature;
struct FeatureDefaultTypeInternal;
extern FeatureDefaultTypeInternal _Feature_default_instance_;
class FeatureList;
struct FeatureListDefaultTypeInternal;
extern FeatureListDefaultTypeInternal _FeatureList_default_instance_;
class FeatureLists;
struct FeatureListsDefaultTypeInternal;
extern FeatureListsDefaultTypeInternal _FeatureLists_default_instance_;
class FeatureLists_FeatureListEntry_DoNotUse;
struct FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal;
extern FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal _FeatureLists_FeatureListEntry_DoNotUse_default_instance_;
class Features;
struct FeaturesDefaultTypeInternal;
extern FeaturesDefaultTypeInternal _Features_default_instance_;
class Features_FeatureEntry_DoNotUse;
struct Features_FeatureEntry_DoNotUseDefaultTypeInternal;
extern Features_FeatureEntry_DoNotUseDefaultTypeInternal _Features_FeatureEntry_DoNotUse_default_instance_;
class FloatList;
struct FloatListDefaultTypeInternal;
extern FloatListDefaultTypeInternal _FloatList_default_instance_;
class Int64List;
struct Int64ListDefaultTypeInternal;
extern Int64ListDefaultTypeInternal _Int64List_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BytesList* Arena::CreateMaybeMessage<::tensorflow::BytesList>(Arena*);
template<> ::tensorflow::Feature* Arena::CreateMaybeMessage<::tensorflow::Feature>(Arena*);
template<> ::tensorflow::FeatureList* Arena::CreateMaybeMessage<::tensorflow::FeatureList>(Arena*);
template<> ::tensorflow::FeatureLists* Arena::CreateMaybeMessage<::tensorflow::FeatureLists>(Arena*);
template<> ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FeatureLists_FeatureListEntry_DoNotUse>(Arena*);
template<> ::tensorflow::Features* Arena::CreateMaybeMessage<::tensorflow::Features>(Arena*);
template<> ::tensorflow::Features_FeatureEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::Features_FeatureEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FloatList* Arena::CreateMaybeMessage<::tensorflow::FloatList>(Arena*);
template<> ::tensorflow::Int64List* Arena::CreateMaybeMessage<::tensorflow::Int64List>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class BytesList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BytesList) */ {
 public:
  inline BytesList() : BytesList(nullptr) {}
  ~BytesList() override;
  explicit PROTOBUF_CONSTEXPR BytesList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BytesList(const BytesList& from);
  BytesList(BytesList&& from) noexcept
    : BytesList() {
    *this = ::std::move(from);
  }

  inline BytesList& operator=(const BytesList& from) {
    CopyFrom(from);
    return *this;
  }
  inline BytesList& operator=(BytesList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BytesList& default_instance() {
    return *internal_default_instance();
  }
  static inline const BytesList* internal_default_instance() {
    return reinterpret_cast<const BytesList*>(
               &_BytesList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(BytesList& a, BytesList& b) {
    a.Swap(&b);
  }
  inline void Swap(BytesList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BytesList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BytesList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BytesList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BytesList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BytesList& from) {
    BytesList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BytesList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BytesList";
  }
  protected:
  explicit BytesList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated bytes value = 1;
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  const std::string& value(int index) const;
  std::string* mutable_value(int index);
  void set_value(int index, const std::string& value);
  void set_value(int index, std::string&& value);
  void set_value(int index, const char* value);
  void set_value(int index, const void* value, size_t size);
  std::string* add_value();
  void add_value(const std::string& value);
  void add_value(std::string&& value);
  void add_value(const char* value);
  void add_value(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_value();
  private:
  const std::string& _internal_value(int index) const;
  std::string* _internal_add_value();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BytesList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class FloatList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FloatList) */ {
 public:
  inline FloatList() : FloatList(nullptr) {}
  ~FloatList() override;
  explicit PROTOBUF_CONSTEXPR FloatList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FloatList(const FloatList& from);
  FloatList(FloatList&& from) noexcept
    : FloatList() {
    *this = ::std::move(from);
  }

  inline FloatList& operator=(const FloatList& from) {
    CopyFrom(from);
    return *this;
  }
  inline FloatList& operator=(FloatList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FloatList& default_instance() {
    return *internal_default_instance();
  }
  static inline const FloatList* internal_default_instance() {
    return reinterpret_cast<const FloatList*>(
               &_FloatList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FloatList& a, FloatList& b) {
    a.Swap(&b);
  }
  inline void Swap(FloatList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FloatList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FloatList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FloatList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FloatList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FloatList& from) {
    FloatList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FloatList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FloatList";
  }
  protected:
  explicit FloatList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated float value = 1 [packed = true];
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  private:
  float _internal_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_value() const;
  void _internal_add_value(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_value();
  public:
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.FloatList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class Int64List final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Int64List) */ {
 public:
  inline Int64List() : Int64List(nullptr) {}
  ~Int64List() override;
  explicit PROTOBUF_CONSTEXPR Int64List(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Int64List(const Int64List& from);
  Int64List(Int64List&& from) noexcept
    : Int64List() {
    *this = ::std::move(from);
  }

  inline Int64List& operator=(const Int64List& from) {
    CopyFrom(from);
    return *this;
  }
  inline Int64List& operator=(Int64List&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Int64List& default_instance() {
    return *internal_default_instance();
  }
  static inline const Int64List* internal_default_instance() {
    return reinterpret_cast<const Int64List*>(
               &_Int64List_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Int64List& a, Int64List& b) {
    a.Swap(&b);
  }
  inline void Swap(Int64List* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Int64List* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Int64List* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Int64List>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Int64List& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Int64List& from) {
    Int64List::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Int64List* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Int64List";
  }
  protected:
  explicit Int64List(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated int64 value = 1 [packed = true];
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  private:
  int64_t _internal_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_value() const;
  void _internal_add_value(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_value();
  public:
  int64_t value(int index) const;
  void set_value(int index, int64_t value);
  void add_value(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.Int64List)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > value_;
    mutable std::atomic<int> _value_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class Feature final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Feature) */ {
 public:
  inline Feature() : Feature(nullptr) {}
  ~Feature() override;
  explicit PROTOBUF_CONSTEXPR Feature(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Feature(const Feature& from);
  Feature(Feature&& from) noexcept
    : Feature() {
    *this = ::std::move(from);
  }

  inline Feature& operator=(const Feature& from) {
    CopyFrom(from);
    return *this;
  }
  inline Feature& operator=(Feature&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Feature& default_instance() {
    return *internal_default_instance();
  }
  enum KindCase {
    kBytesList = 1,
    kFloatList = 2,
    kInt64List = 3,
    KIND_NOT_SET = 0,
  };

  static inline const Feature* internal_default_instance() {
    return reinterpret_cast<const Feature*>(
               &_Feature_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Feature& a, Feature& b) {
    a.Swap(&b);
  }
  inline void Swap(Feature* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Feature* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Feature* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Feature>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Feature& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Feature& from) {
    Feature::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Feature* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Feature";
  }
  protected:
  explicit Feature(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBytesListFieldNumber = 1,
    kFloatListFieldNumber = 2,
    kInt64ListFieldNumber = 3,
  };
  // .tensorflow.BytesList bytes_list = 1;
  bool has_bytes_list() const;
  private:
  bool _internal_has_bytes_list() const;
  public:
  void clear_bytes_list();
  const ::tensorflow::BytesList& bytes_list() const;
  PROTOBUF_NODISCARD ::tensorflow::BytesList* release_bytes_list();
  ::tensorflow::BytesList* mutable_bytes_list();
  void set_allocated_bytes_list(::tensorflow::BytesList* bytes_list);
  private:
  const ::tensorflow::BytesList& _internal_bytes_list() const;
  ::tensorflow::BytesList* _internal_mutable_bytes_list();
  public:
  void unsafe_arena_set_allocated_bytes_list(
      ::tensorflow::BytesList* bytes_list);
  ::tensorflow::BytesList* unsafe_arena_release_bytes_list();

  // .tensorflow.FloatList float_list = 2;
  bool has_float_list() const;
  private:
  bool _internal_has_float_list() const;
  public:
  void clear_float_list();
  const ::tensorflow::FloatList& float_list() const;
  PROTOBUF_NODISCARD ::tensorflow::FloatList* release_float_list();
  ::tensorflow::FloatList* mutable_float_list();
  void set_allocated_float_list(::tensorflow::FloatList* float_list);
  private:
  const ::tensorflow::FloatList& _internal_float_list() const;
  ::tensorflow::FloatList* _internal_mutable_float_list();
  public:
  void unsafe_arena_set_allocated_float_list(
      ::tensorflow::FloatList* float_list);
  ::tensorflow::FloatList* unsafe_arena_release_float_list();

  // .tensorflow.Int64List int64_list = 3;
  bool has_int64_list() const;
  private:
  bool _internal_has_int64_list() const;
  public:
  void clear_int64_list();
  const ::tensorflow::Int64List& int64_list() const;
  PROTOBUF_NODISCARD ::tensorflow::Int64List* release_int64_list();
  ::tensorflow::Int64List* mutable_int64_list();
  void set_allocated_int64_list(::tensorflow::Int64List* int64_list);
  private:
  const ::tensorflow::Int64List& _internal_int64_list() const;
  ::tensorflow::Int64List* _internal_mutable_int64_list();
  public:
  void unsafe_arena_set_allocated_int64_list(
      ::tensorflow::Int64List* int64_list);
  ::tensorflow::Int64List* unsafe_arena_release_int64_list();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Feature)
 private:
  class _Internal;
  void set_has_bytes_list();
  void set_has_float_list();
  void set_has_int64_list();

  inline bool has_kind() const;
  inline void clear_has_kind();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union KindUnion {
      constexpr KindUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::BytesList* bytes_list_;
      ::tensorflow::FloatList* float_list_;
      ::tensorflow::Int64List* int64_list_;
    } kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class Features_FeatureEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Features_FeatureEntry_DoNotUse, 
    std::string, ::tensorflow::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Features_FeatureEntry_DoNotUse, 
    std::string, ::tensorflow::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  Features_FeatureEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR Features_FeatureEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Features_FeatureEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Features_FeatureEntry_DoNotUse& other);
  static const Features_FeatureEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Features_FeatureEntry_DoNotUse*>(&_Features_FeatureEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.Features.FeatureEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};

// -------------------------------------------------------------------

class Features final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Features) */ {
 public:
  inline Features() : Features(nullptr) {}
  ~Features() override;
  explicit PROTOBUF_CONSTEXPR Features(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Features(const Features& from);
  Features(Features&& from) noexcept
    : Features() {
    *this = ::std::move(from);
  }

  inline Features& operator=(const Features& from) {
    CopyFrom(from);
    return *this;
  }
  inline Features& operator=(Features&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Features& default_instance() {
    return *internal_default_instance();
  }
  static inline const Features* internal_default_instance() {
    return reinterpret_cast<const Features*>(
               &_Features_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Features& a, Features& b) {
    a.Swap(&b);
  }
  inline void Swap(Features* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Features* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Features* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Features>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Features& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Features& from) {
    Features::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Features* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Features";
  }
  protected:
  explicit Features(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 1,
  };
  // map<string, .tensorflow.Feature> feature = 1;
  int feature_size() const;
  private:
  int _internal_feature_size() const;
  public:
  void clear_feature();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >&
      _internal_feature() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >*
      _internal_mutable_feature();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >&
      feature() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >*
      mutable_feature();

  // @@protoc_insertion_point(class_scope:tensorflow.Features)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        Features_FeatureEntry_DoNotUse,
        std::string, ::tensorflow::Feature,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> feature_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class FeatureList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureList) */ {
 public:
  inline FeatureList() : FeatureList(nullptr) {}
  ~FeatureList() override;
  explicit PROTOBUF_CONSTEXPR FeatureList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FeatureList(const FeatureList& from);
  FeatureList(FeatureList&& from) noexcept
    : FeatureList() {
    *this = ::std::move(from);
  }

  inline FeatureList& operator=(const FeatureList& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureList& operator=(FeatureList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FeatureList& default_instance() {
    return *internal_default_instance();
  }
  static inline const FeatureList* internal_default_instance() {
    return reinterpret_cast<const FeatureList*>(
               &_FeatureList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(FeatureList& a, FeatureList& b) {
    a.Swap(&b);
  }
  inline void Swap(FeatureList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FeatureList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FeatureList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FeatureList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FeatureList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FeatureList& from) {
    FeatureList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FeatureList";
  }
  protected:
  explicit FeatureList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 1,
  };
  // repeated .tensorflow.Feature feature = 1;
  int feature_size() const;
  private:
  int _internal_feature_size() const;
  public:
  void clear_feature();
  ::tensorflow::Feature* mutable_feature(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >*
      mutable_feature();
  private:
  const ::tensorflow::Feature& _internal_feature(int index) const;
  ::tensorflow::Feature* _internal_add_feature();
  public:
  const ::tensorflow::Feature& feature(int index) const;
  ::tensorflow::Feature* add_feature();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >&
      feature() const;

  // @@protoc_insertion_point(class_scope:tensorflow.FeatureList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature > feature_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// -------------------------------------------------------------------

class FeatureLists_FeatureListEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureLists_FeatureListEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureList,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureLists_FeatureListEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureList,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  FeatureLists_FeatureListEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FeatureLists_FeatureListEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FeatureLists_FeatureListEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FeatureLists_FeatureListEntry_DoNotUse& other);
  static const FeatureLists_FeatureListEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureLists_FeatureListEntry_DoNotUse*>(&_FeatureLists_FeatureListEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FeatureLists.FeatureListEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};

// -------------------------------------------------------------------

class FeatureLists final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureLists) */ {
 public:
  inline FeatureLists() : FeatureLists(nullptr) {}
  ~FeatureLists() override;
  explicit PROTOBUF_CONSTEXPR FeatureLists(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FeatureLists(const FeatureLists& from);
  FeatureLists(FeatureLists&& from) noexcept
    : FeatureLists() {
    *this = ::std::move(from);
  }

  inline FeatureLists& operator=(const FeatureLists& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureLists& operator=(FeatureLists&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FeatureLists& default_instance() {
    return *internal_default_instance();
  }
  static inline const FeatureLists* internal_default_instance() {
    return reinterpret_cast<const FeatureLists*>(
               &_FeatureLists_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(FeatureLists& a, FeatureLists& b) {
    a.Swap(&b);
  }
  inline void Swap(FeatureLists* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FeatureLists* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FeatureLists* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FeatureLists>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FeatureLists& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FeatureLists& from) {
    FeatureLists::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureLists* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FeatureLists";
  }
  protected:
  explicit FeatureLists(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeatureListFieldNumber = 1,
  };
  // map<string, .tensorflow.FeatureList> feature_list = 1;
  int feature_list_size() const;
  private:
  int _internal_feature_list_size() const;
  public:
  void clear_feature_list();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >&
      _internal_feature_list() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >*
      _internal_mutable_feature_list();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >&
      feature_list() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >*
      mutable_feature_list();

  // @@protoc_insertion_point(class_scope:tensorflow.FeatureLists)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FeatureLists_FeatureListEntry_DoNotUse,
        std::string, ::tensorflow::FeatureList,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> feature_list_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2ffeature_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BytesList

// repeated bytes value = 1;
inline int BytesList::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int BytesList::value_size() const {
  return _internal_value_size();
}
inline void BytesList::clear_value() {
  _impl_.value_.Clear();
}
inline std::string* BytesList::add_value() {
  std::string* _s = _internal_add_value();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BytesList.value)
  return _s;
}
inline const std::string& BytesList::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline const std::string& BytesList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BytesList.value)
  return _internal_value(index);
}
inline std::string* BytesList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BytesList.value)
  return _impl_.value_.Mutable(index);
}
inline void BytesList::set_value(int index, const std::string& value) {
  _impl_.value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.BytesList.value)
}
inline void BytesList::set_value(int index, std::string&& value) {
  _impl_.value_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.BytesList.value)
}
inline void BytesList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BytesList.value)
}
inline void BytesList::set_value(int index, const void* value, size_t size) {
  _impl_.value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BytesList.value)
}
inline std::string* BytesList::_internal_add_value() {
  return _impl_.value_.Add();
}
inline void BytesList::add_value(const std::string& value) {
  _impl_.value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BytesList.value)
}
inline void BytesList::add_value(std::string&& value) {
  _impl_.value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BytesList.value)
}
inline void BytesList::add_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BytesList.value)
}
inline void BytesList::add_value(const void* value, size_t size) {
  _impl_.value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BytesList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
BytesList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.BytesList.value)
  return _impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
BytesList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BytesList.value)
  return &_impl_.value_;
}

// -------------------------------------------------------------------

// FloatList

// repeated float value = 1 [packed = true];
inline int FloatList::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int FloatList::value_size() const {
  return _internal_value_size();
}
inline void FloatList::clear_value() {
  _impl_.value_.Clear();
}
inline float FloatList::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline float FloatList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FloatList.value)
  return _internal_value(index);
}
inline void FloatList::set_value(int index, float value) {
  _impl_.value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.FloatList.value)
}
inline void FloatList::_internal_add_value(float value) {
  _impl_.value_.Add(value);
}
inline void FloatList::add_value(float value) {
  _internal_add_value(value);
  // @@protoc_insertion_point(field_add:tensorflow.FloatList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
FloatList::_internal_value() const {
  return _impl_.value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
FloatList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.FloatList.value)
  return _internal_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
FloatList::_internal_mutable_value() {
  return &_impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
FloatList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FloatList.value)
  return _internal_mutable_value();
}

// -------------------------------------------------------------------

// Int64List

// repeated int64 value = 1 [packed = true];
inline int Int64List::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int Int64List::value_size() const {
  return _internal_value_size();
}
inline void Int64List::clear_value() {
  _impl_.value_.Clear();
}
inline int64_t Int64List::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline int64_t Int64List::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Int64List.value)
  return _internal_value(index);
}
inline void Int64List::set_value(int index, int64_t value) {
  _impl_.value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Int64List.value)
}
inline void Int64List::_internal_add_value(int64_t value) {
  _impl_.value_.Add(value);
}
inline void Int64List::add_value(int64_t value) {
  _internal_add_value(value);
  // @@protoc_insertion_point(field_add:tensorflow.Int64List.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Int64List::_internal_value() const {
  return _impl_.value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Int64List::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.Int64List.value)
  return _internal_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Int64List::_internal_mutable_value() {
  return &_impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Int64List::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Int64List.value)
  return _internal_mutable_value();
}

// -------------------------------------------------------------------

// Feature

// .tensorflow.BytesList bytes_list = 1;
inline bool Feature::_internal_has_bytes_list() const {
  return kind_case() == kBytesList;
}
inline bool Feature::has_bytes_list() const {
  return _internal_has_bytes_list();
}
inline void Feature::set_has_bytes_list() {
  _impl_._oneof_case_[0] = kBytesList;
}
inline void Feature::clear_bytes_list() {
  if (_internal_has_bytes_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.bytes_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::BytesList* Feature::release_bytes_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.bytes_list)
  if (_internal_has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::BytesList* temp = _impl_.kind_.bytes_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.bytes_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::BytesList& Feature::_internal_bytes_list() const {
  return _internal_has_bytes_list()
      ? *_impl_.kind_.bytes_list_
      : reinterpret_cast< ::tensorflow::BytesList&>(::tensorflow::_BytesList_default_instance_);
}
inline const ::tensorflow::BytesList& Feature::bytes_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.bytes_list)
  return _internal_bytes_list();
}
inline ::tensorflow::BytesList* Feature::unsafe_arena_release_bytes_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.bytes_list)
  if (_internal_has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::BytesList* temp = _impl_.kind_.bytes_list_;
    _impl_.kind_.bytes_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Feature::unsafe_arena_set_allocated_bytes_list(::tensorflow::BytesList* bytes_list) {
  clear_kind();
  if (bytes_list) {
    set_has_bytes_list();
    _impl_.kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.bytes_list)
}
inline ::tensorflow::BytesList* Feature::_internal_mutable_bytes_list() {
  if (!_internal_has_bytes_list()) {
    clear_kind();
    set_has_bytes_list();
    _impl_.kind_.bytes_list_ = CreateMaybeMessage< ::tensorflow::BytesList >(GetArenaForAllocation());
  }
  return _impl_.kind_.bytes_list_;
}
inline ::tensorflow::BytesList* Feature::mutable_bytes_list() {
  ::tensorflow::BytesList* _msg = _internal_mutable_bytes_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.bytes_list)
  return _msg;
}

// .tensorflow.FloatList float_list = 2;
inline bool Feature::_internal_has_float_list() const {
  return kind_case() == kFloatList;
}
inline bool Feature::has_float_list() const {
  return _internal_has_float_list();
}
inline void Feature::set_has_float_list() {
  _impl_._oneof_case_[0] = kFloatList;
}
inline void Feature::clear_float_list() {
  if (_internal_has_float_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.float_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::FloatList* Feature::release_float_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.float_list)
  if (_internal_has_float_list()) {
    clear_has_kind();
    ::tensorflow::FloatList* temp = _impl_.kind_.float_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.float_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::FloatList& Feature::_internal_float_list() const {
  return _internal_has_float_list()
      ? *_impl_.kind_.float_list_
      : reinterpret_cast< ::tensorflow::FloatList&>(::tensorflow::_FloatList_default_instance_);
}
inline const ::tensorflow::FloatList& Feature::float_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.float_list)
  return _internal_float_list();
}
inline ::tensorflow::FloatList* Feature::unsafe_arena_release_float_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.float_list)
  if (_internal_has_float_list()) {
    clear_has_kind();
    ::tensorflow::FloatList* temp = _impl_.kind_.float_list_;
    _impl_.kind_.float_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Feature::unsafe_arena_set_allocated_float_list(::tensorflow::FloatList* float_list) {
  clear_kind();
  if (float_list) {
    set_has_float_list();
    _impl_.kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.float_list)
}
inline ::tensorflow::FloatList* Feature::_internal_mutable_float_list() {
  if (!_internal_has_float_list()) {
    clear_kind();
    set_has_float_list();
    _impl_.kind_.float_list_ = CreateMaybeMessage< ::tensorflow::FloatList >(GetArenaForAllocation());
  }
  return _impl_.kind_.float_list_;
}
inline ::tensorflow::FloatList* Feature::mutable_float_list() {
  ::tensorflow::FloatList* _msg = _internal_mutable_float_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.float_list)
  return _msg;
}

// .tensorflow.Int64List int64_list = 3;
inline bool Feature::_internal_has_int64_list() const {
  return kind_case() == kInt64List;
}
inline bool Feature::has_int64_list() const {
  return _internal_has_int64_list();
}
inline void Feature::set_has_int64_list() {
  _impl_._oneof_case_[0] = kInt64List;
}
inline void Feature::clear_int64_list() {
  if (_internal_has_int64_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.int64_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::Int64List* Feature::release_int64_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.int64_list)
  if (_internal_has_int64_list()) {
    clear_has_kind();
    ::tensorflow::Int64List* temp = _impl_.kind_.int64_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.int64_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Int64List& Feature::_internal_int64_list() const {
  return _internal_has_int64_list()
      ? *_impl_.kind_.int64_list_
      : reinterpret_cast< ::tensorflow::Int64List&>(::tensorflow::_Int64List_default_instance_);
}
inline const ::tensorflow::Int64List& Feature::int64_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.int64_list)
  return _internal_int64_list();
}
inline ::tensorflow::Int64List* Feature::unsafe_arena_release_int64_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.int64_list)
  if (_internal_has_int64_list()) {
    clear_has_kind();
    ::tensorflow::Int64List* temp = _impl_.kind_.int64_list_;
    _impl_.kind_.int64_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Feature::unsafe_arena_set_allocated_int64_list(::tensorflow::Int64List* int64_list) {
  clear_kind();
  if (int64_list) {
    set_has_int64_list();
    _impl_.kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.int64_list)
}
inline ::tensorflow::Int64List* Feature::_internal_mutable_int64_list() {
  if (!_internal_has_int64_list()) {
    clear_kind();
    set_has_int64_list();
    _impl_.kind_.int64_list_ = CreateMaybeMessage< ::tensorflow::Int64List >(GetArenaForAllocation());
  }
  return _impl_.kind_.int64_list_;
}
inline ::tensorflow::Int64List* Feature::mutable_int64_list() {
  ::tensorflow::Int64List* _msg = _internal_mutable_int64_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.int64_list)
  return _msg;
}

inline bool Feature::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void Feature::clear_has_kind() {
  _impl_._oneof_case_[0] = KIND_NOT_SET;
}
inline Feature::KindCase Feature::kind_case() const {
  return Feature::KindCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Features

// map<string, .tensorflow.Feature> feature = 1;
inline int Features::_internal_feature_size() const {
  return _impl_.feature_.size();
}
inline int Features::feature_size() const {
  return _internal_feature_size();
}
inline void Features::clear_feature() {
  _impl_.feature_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >&
Features::_internal_feature() const {
  return _impl_.feature_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >&
Features::feature() const {
  // @@protoc_insertion_point(field_map:tensorflow.Features.feature)
  return _internal_feature();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >*
Features::_internal_mutable_feature() {
  return _impl_.feature_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::Feature >*
Features::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.Features.feature)
  return _internal_mutable_feature();
}

// -------------------------------------------------------------------

// FeatureList

// repeated .tensorflow.Feature feature = 1;
inline int FeatureList::_internal_feature_size() const {
  return _impl_.feature_.size();
}
inline int FeatureList::feature_size() const {
  return _internal_feature_size();
}
inline void FeatureList::clear_feature() {
  _impl_.feature_.Clear();
}
inline ::tensorflow::Feature* FeatureList::mutable_feature(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureList.feature)
  return _impl_.feature_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >*
FeatureList::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FeatureList.feature)
  return &_impl_.feature_;
}
inline const ::tensorflow::Feature& FeatureList::_internal_feature(int index) const {
  return _impl_.feature_.Get(index);
}
inline const ::tensorflow::Feature& FeatureList::feature(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureList.feature)
  return _internal_feature(index);
}
inline ::tensorflow::Feature* FeatureList::_internal_add_feature() {
  return _impl_.feature_.Add();
}
inline ::tensorflow::Feature* FeatureList::add_feature() {
  ::tensorflow::Feature* _add = _internal_add_feature();
  // @@protoc_insertion_point(field_add:tensorflow.FeatureList.feature)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Feature >&
FeatureList::feature() const {
  // @@protoc_insertion_point(field_list:tensorflow.FeatureList.feature)
  return _impl_.feature_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FeatureLists

// map<string, .tensorflow.FeatureList> feature_list = 1;
inline int FeatureLists::_internal_feature_list_size() const {
  return _impl_.feature_list_.size();
}
inline int FeatureLists::feature_list_size() const {
  return _internal_feature_list_size();
}
inline void FeatureLists::clear_feature_list() {
  _impl_.feature_list_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >&
FeatureLists::_internal_feature_list() const {
  return _impl_.feature_list_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >&
FeatureLists::feature_list() const {
  // @@protoc_insertion_point(field_map:tensorflow.FeatureLists.feature_list)
  return _internal_feature_list();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >*
FeatureLists::_internal_mutable_feature_list() {
  return _impl_.feature_list_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureList >*
FeatureLists::mutable_feature_list() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FeatureLists.feature_list)
  return _internal_mutable_feature_list();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto
