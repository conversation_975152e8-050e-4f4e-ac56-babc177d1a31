from googletrans import Translator

class MultilingualTranslator:
    
    def __init__(self):
        self.translator = Translator()
        
        # Pre-translated responses for better accuracy
        self.responses = {
            'hi': {
                'greet': "नमस्ते! मैं डॉ.बॉट हूँ, आपका स्वास्थ्य साथी। आज मैं आपकी कैसे सहायता कर सकता हूँ?",
                'symptom': "मैं समझ सकता हूँ कि आप लक्षण महसूस कर रहे हैं। बुखार के लिए आराम करें और तरल पदार्थ पिएं। लगातार लक्षणों के लिए तुरंत डॉक्टर से सलाह लें।",
                'goodbye': "ख्याल रखें! आपातकाल के लिए अपनी स्थानीय स्वास्थ्य हेल्पलाइन पर कॉल करें।"
            }
        }
    
    def translate_to_language(self, text: str, target_lang: str) -> str:
        """Translate text to target language"""
        try:
            if target_lang == 'en':
                return text
            
            # Use pre-defined responses if available
            if target_lang == 'hi':
                if 'health companion' in text.lower():
                    return self.responses['hi']['greet']
                elif 'symptoms' in text.lower():
                    return self.responses['hi']['symptom']
                elif 'take care' in text.lower():
                    return self.responses['hi']['goodbye']
            
            # Fall back to Google Translate
            result = self.translator.translate(text, dest=target_lang)
            return result.text
            
        except Exception as e:
            print(f"Translation error: {e}")
            return text
