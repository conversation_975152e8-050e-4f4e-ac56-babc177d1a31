from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet
from language_utils import LanguageDetector
from translation import MultilingualTranslator
import mysql.connector

class ActionMultilingualResponse(Action):

    def name(self) -> Text:
        return "action_multilingual_response"

    def __init__(self):
        self.detector = LanguageDetector()
        self.translator = MultilingualTranslator()

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        # Get user message and detect language
        user_message = tracker.latest_message.get('text')
        detected_language = self.detector.detect_language(user_message)

        # Get intent
        intent = tracker.latest_message.get('intent', {}).get('name')

        # Generate response based on intent
        if intent == 'greet':
            english_response = "Hello! I'm Dr.<PERSON><PERSON>, your health companion. How can I help you today?"
        elif intent == 'ask_symptom':
            english_response = "I understand you're experiencing symptoms. For fever, rest and drink fluids. For persistent symptoms, consult a doctor immediately."
        elif intent == 'ask_medicine':
            english_response = "For common fever, adults can take Paracetamol 500mg every 6 hours. Always consult a healthcare professional."
        elif intent == 'ask_vaccination':
            english_response = "Vaccinations are important for preventing diseases. Please consult your healthcare provider for vaccination schedules and recommendations."
        elif intent == 'ask_disease_info':
            english_response = "I can provide general information about diseases. For specific medical advice, please consult a qualified healthcare professional."
        elif intent == 'goodbye':
            english_response = "Take care! Remember, for emergencies call your local health helpline. Stay healthy!"
        else:
            english_response = "I'm here to help with your health questions. Please tell me your symptoms or ask about medicines."

        # Translate response to detected language
        final_response = self.translator.translate_to_language(english_response, detected_language)

        # Send response
        dispatcher.utter_message(text=final_response)

        # Log conversation to MySQL database
        self._log_conversation(user_message, intent, detected_language, final_response)

        return [SlotSet("user_language", detected_language)]

    def _log_conversation(self, user_msg, intent, language, bot_response):
        """Log multilingual conversation to MySQL database"""
        try:
            conn = mysql.connector.connect(
                host="localhost",
                user="root",          # Replace with your MySQL username
                password="Ragul@732007",  # Replace with your MySQL password
                database="drbot_db"
            )

            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO multilingual_chat_logs
                (user_message, intent, detected_language, bot_response, timestamp)
                VALUES (%s, %s, %s, %s, NOW())
            """, (user_msg, intent, language, bot_response))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"MySQL logging error: {e}")
