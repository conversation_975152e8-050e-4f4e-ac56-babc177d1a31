/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_QUANTCONVERTCONST
#define GEN_PASS_DECL_QUANTCONVERTSIMULATEDQUANT
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// QuantConvertConst
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_QUANTCONVERTCONST
#undef GEN_PASS_DECL_QUANTCONVERTCONST
#endif // GEN_PASS_DECL_QUANTCONVERTCONST
#ifdef GEN_PASS_DEF_QUANTCONVERTCONST
namespace impl {

template <typename DerivedT>
class QuantConvertConstBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = QuantConvertConstBase;

  QuantConvertConstBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  QuantConvertConstBase(const QuantConvertConstBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-const");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-const"; }

  ::llvm::StringRef getDescription() const override { return "Converts constants followed by qbarrier to actual quantized values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("QuantConvertConst");
  }
  ::llvm::StringRef getName() const override { return "QuantConvertConst"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(QuantConvertConstBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_QUANTCONVERTCONST
#endif // GEN_PASS_DEF_QUANTCONVERTCONST

//===----------------------------------------------------------------------===//
// QuantConvertSimulatedQuant
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_QUANTCONVERTSIMULATEDQUANT
#undef GEN_PASS_DECL_QUANTCONVERTSIMULATEDQUANT
#endif // GEN_PASS_DECL_QUANTCONVERTSIMULATEDQUANT
#ifdef GEN_PASS_DEF_QUANTCONVERTSIMULATEDQUANT
namespace impl {

template <typename DerivedT>
class QuantConvertSimulatedQuantBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = QuantConvertSimulatedQuantBase;

  QuantConvertSimulatedQuantBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  QuantConvertSimulatedQuantBase(const QuantConvertSimulatedQuantBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-simulated-quantization");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-simulated-quantization"; }

  ::llvm::StringRef getDescription() const override { return "Converts training-time simulated quantization ops to corresponding quantize/dequantize casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("QuantConvertSimulatedQuant");
  }
  ::llvm::StringRef getName() const override { return "QuantConvertSimulatedQuant"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(QuantConvertSimulatedQuantBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_QUANTCONVERTSIMULATEDQUANT
#endif // GEN_PASS_DEF_QUANTCONVERTSIMULATEDQUANT
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// QuantConvertConst Registration
//===----------------------------------------------------------------------===//

inline void registerQuantConvertConst() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quantfork::createConvertConstPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerQuantConvertConstPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quantfork::createConvertConstPass();
  });
}

//===----------------------------------------------------------------------===//
// QuantConvertSimulatedQuant Registration
//===----------------------------------------------------------------------===//

inline void registerQuantConvertSimulatedQuant() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quantfork::createConvertSimulatedQuantPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerQuantConvertSimulatedQuantPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::quantfork::createConvertSimulatedQuantPass();
  });
}

//===----------------------------------------------------------------------===//
// quantfork Registration
//===----------------------------------------------------------------------===//

inline void registerquantforkPasses() {
  registerQuantConvertConst();
  registerQuantConvertSimulatedQuant();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class QuantConvertConstBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = QuantConvertConstBase;

  QuantConvertConstBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  QuantConvertConstBase(const QuantConvertConstBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-const");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-const"; }

  ::llvm::StringRef getDescription() const override { return "Converts constants followed by qbarrier to actual quantized values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("QuantConvertConst");
  }
  ::llvm::StringRef getName() const override { return "QuantConvertConst"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(QuantConvertConstBase<DerivedT>)

protected:
};

template <typename DerivedT>
class QuantConvertSimulatedQuantBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = QuantConvertSimulatedQuantBase;

  QuantConvertSimulatedQuantBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  QuantConvertSimulatedQuantBase(const QuantConvertSimulatedQuantBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("quant-convert-simulated-quantization");
  }
  ::llvm::StringRef getArgument() const override { return "quant-convert-simulated-quantization"; }

  ::llvm::StringRef getDescription() const override { return "Converts training-time simulated quantization ops to corresponding quantize/dequantize casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("QuantConvertSimulatedQuant");
  }
  ::llvm::StringRef getName() const override { return "QuantConvertSimulatedQuant"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(QuantConvertSimulatedQuantBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
