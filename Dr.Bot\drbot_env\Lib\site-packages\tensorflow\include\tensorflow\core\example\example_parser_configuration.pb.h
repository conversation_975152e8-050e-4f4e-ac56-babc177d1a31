// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/example_parser_configuration.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
namespace tensorflow {
class ExampleParserConfiguration;
struct ExampleParserConfigurationDefaultTypeInternal;
extern ExampleParserConfigurationDefaultTypeInternal _ExampleParserConfiguration_default_instance_;
class ExampleParserConfiguration_FeatureMapEntry_DoNotUse;
struct ExampleParserConfiguration_FeatureMapEntry_DoNotUseDefaultTypeInternal;
extern ExampleParserConfiguration_FeatureMapEntry_DoNotUseDefaultTypeInternal _ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_;
class FeatureConfiguration;
struct FeatureConfigurationDefaultTypeInternal;
extern FeatureConfigurationDefaultTypeInternal _FeatureConfiguration_default_instance_;
class FixedLenFeatureProto;
struct FixedLenFeatureProtoDefaultTypeInternal;
extern FixedLenFeatureProtoDefaultTypeInternal _FixedLenFeatureProto_default_instance_;
class VarLenFeatureProto;
struct VarLenFeatureProtoDefaultTypeInternal;
extern VarLenFeatureProtoDefaultTypeInternal _VarLenFeatureProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ExampleParserConfiguration* Arena::CreateMaybeMessage<::tensorflow::ExampleParserConfiguration>(Arena*);
template<> ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FeatureConfiguration* Arena::CreateMaybeMessage<::tensorflow::FeatureConfiguration>(Arena*);
template<> ::tensorflow::FixedLenFeatureProto* Arena::CreateMaybeMessage<::tensorflow::FixedLenFeatureProto>(Arena*);
template<> ::tensorflow::VarLenFeatureProto* Arena::CreateMaybeMessage<::tensorflow::VarLenFeatureProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class VarLenFeatureProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.VarLenFeatureProto) */ {
 public:
  inline VarLenFeatureProto() : VarLenFeatureProto(nullptr) {}
  ~VarLenFeatureProto() override;
  explicit PROTOBUF_CONSTEXPR VarLenFeatureProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VarLenFeatureProto(const VarLenFeatureProto& from);
  VarLenFeatureProto(VarLenFeatureProto&& from) noexcept
    : VarLenFeatureProto() {
    *this = ::std::move(from);
  }

  inline VarLenFeatureProto& operator=(const VarLenFeatureProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline VarLenFeatureProto& operator=(VarLenFeatureProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VarLenFeatureProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const VarLenFeatureProto* internal_default_instance() {
    return reinterpret_cast<const VarLenFeatureProto*>(
               &_VarLenFeatureProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(VarLenFeatureProto& a, VarLenFeatureProto& b) {
    a.Swap(&b);
  }
  inline void Swap(VarLenFeatureProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VarLenFeatureProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VarLenFeatureProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VarLenFeatureProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VarLenFeatureProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const VarLenFeatureProto& from) {
    VarLenFeatureProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VarLenFeatureProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.VarLenFeatureProto";
  }
  protected:
  explicit VarLenFeatureProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesOutputTensorNameFieldNumber = 2,
    kIndicesOutputTensorNameFieldNumber = 3,
    kShapesOutputTensorNameFieldNumber = 4,
    kDtypeFieldNumber = 1,
  };
  // string values_output_tensor_name = 2;
  void clear_values_output_tensor_name();
  const std::string& values_output_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_values_output_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_values_output_tensor_name();
  PROTOBUF_NODISCARD std::string* release_values_output_tensor_name();
  void set_allocated_values_output_tensor_name(std::string* values_output_tensor_name);
  private:
  const std::string& _internal_values_output_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_values_output_tensor_name(const std::string& value);
  std::string* _internal_mutable_values_output_tensor_name();
  public:

  // string indices_output_tensor_name = 3;
  void clear_indices_output_tensor_name();
  const std::string& indices_output_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_indices_output_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_indices_output_tensor_name();
  PROTOBUF_NODISCARD std::string* release_indices_output_tensor_name();
  void set_allocated_indices_output_tensor_name(std::string* indices_output_tensor_name);
  private:
  const std::string& _internal_indices_output_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_indices_output_tensor_name(const std::string& value);
  std::string* _internal_mutable_indices_output_tensor_name();
  public:

  // string shapes_output_tensor_name = 4;
  void clear_shapes_output_tensor_name();
  const std::string& shapes_output_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_shapes_output_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_shapes_output_tensor_name();
  PROTOBUF_NODISCARD std::string* release_shapes_output_tensor_name();
  void set_allocated_shapes_output_tensor_name(std::string* shapes_output_tensor_name);
  private:
  const std::string& _internal_shapes_output_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_shapes_output_tensor_name(const std::string& value);
  std::string* _internal_mutable_shapes_output_tensor_name();
  public:

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.VarLenFeatureProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr values_output_tensor_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr indices_output_tensor_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr shapes_output_tensor_name_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class FixedLenFeatureProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FixedLenFeatureProto) */ {
 public:
  inline FixedLenFeatureProto() : FixedLenFeatureProto(nullptr) {}
  ~FixedLenFeatureProto() override;
  explicit PROTOBUF_CONSTEXPR FixedLenFeatureProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FixedLenFeatureProto(const FixedLenFeatureProto& from);
  FixedLenFeatureProto(FixedLenFeatureProto&& from) noexcept
    : FixedLenFeatureProto() {
    *this = ::std::move(from);
  }

  inline FixedLenFeatureProto& operator=(const FixedLenFeatureProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline FixedLenFeatureProto& operator=(FixedLenFeatureProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FixedLenFeatureProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const FixedLenFeatureProto* internal_default_instance() {
    return reinterpret_cast<const FixedLenFeatureProto*>(
               &_FixedLenFeatureProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FixedLenFeatureProto& a, FixedLenFeatureProto& b) {
    a.Swap(&b);
  }
  inline void Swap(FixedLenFeatureProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FixedLenFeatureProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FixedLenFeatureProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FixedLenFeatureProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FixedLenFeatureProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FixedLenFeatureProto& from) {
    FixedLenFeatureProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FixedLenFeatureProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FixedLenFeatureProto";
  }
  protected:
  explicit FixedLenFeatureProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesOutputTensorNameFieldNumber = 4,
    kShapeFieldNumber = 2,
    kDefaultValueFieldNumber = 3,
    kDtypeFieldNumber = 1,
  };
  // string values_output_tensor_name = 4;
  void clear_values_output_tensor_name();
  const std::string& values_output_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_values_output_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_values_output_tensor_name();
  PROTOBUF_NODISCARD std::string* release_values_output_tensor_name();
  void set_allocated_values_output_tensor_name(std::string* values_output_tensor_name);
  private:
  const std::string& _internal_values_output_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_values_output_tensor_name(const std::string& value);
  std::string* _internal_mutable_values_output_tensor_name();
  public:

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto default_value = 3;
  bool has_default_value() const;
  private:
  bool _internal_has_default_value() const;
  public:
  void clear_default_value();
  const ::tensorflow::TensorProto& default_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_default_value();
  ::tensorflow::TensorProto* mutable_default_value();
  void set_allocated_default_value(::tensorflow::TensorProto* default_value);
  private:
  const ::tensorflow::TensorProto& _internal_default_value() const;
  ::tensorflow::TensorProto* _internal_mutable_default_value();
  public:
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::TensorProto* default_value);
  ::tensorflow::TensorProto* unsafe_arena_release_default_value();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.FixedLenFeatureProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr values_output_tensor_name_;
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::TensorProto* default_value_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class FeatureConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureConfiguration) */ {
 public:
  inline FeatureConfiguration() : FeatureConfiguration(nullptr) {}
  ~FeatureConfiguration() override;
  explicit PROTOBUF_CONSTEXPR FeatureConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FeatureConfiguration(const FeatureConfiguration& from);
  FeatureConfiguration(FeatureConfiguration&& from) noexcept
    : FeatureConfiguration() {
    *this = ::std::move(from);
  }

  inline FeatureConfiguration& operator=(const FeatureConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureConfiguration& operator=(FeatureConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FeatureConfiguration& default_instance() {
    return *internal_default_instance();
  }
  enum ConfigCase {
    kFixedLenFeature = 1,
    kVarLenFeature = 2,
    CONFIG_NOT_SET = 0,
  };

  static inline const FeatureConfiguration* internal_default_instance() {
    return reinterpret_cast<const FeatureConfiguration*>(
               &_FeatureConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FeatureConfiguration& a, FeatureConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(FeatureConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FeatureConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FeatureConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FeatureConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FeatureConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FeatureConfiguration& from) {
    FeatureConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FeatureConfiguration";
  }
  protected:
  explicit FeatureConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFixedLenFeatureFieldNumber = 1,
    kVarLenFeatureFieldNumber = 2,
  };
  // .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
  bool has_fixed_len_feature() const;
  private:
  bool _internal_has_fixed_len_feature() const;
  public:
  void clear_fixed_len_feature();
  const ::tensorflow::FixedLenFeatureProto& fixed_len_feature() const;
  PROTOBUF_NODISCARD ::tensorflow::FixedLenFeatureProto* release_fixed_len_feature();
  ::tensorflow::FixedLenFeatureProto* mutable_fixed_len_feature();
  void set_allocated_fixed_len_feature(::tensorflow::FixedLenFeatureProto* fixed_len_feature);
  private:
  const ::tensorflow::FixedLenFeatureProto& _internal_fixed_len_feature() const;
  ::tensorflow::FixedLenFeatureProto* _internal_mutable_fixed_len_feature();
  public:
  void unsafe_arena_set_allocated_fixed_len_feature(
      ::tensorflow::FixedLenFeatureProto* fixed_len_feature);
  ::tensorflow::FixedLenFeatureProto* unsafe_arena_release_fixed_len_feature();

  // .tensorflow.VarLenFeatureProto var_len_feature = 2;
  bool has_var_len_feature() const;
  private:
  bool _internal_has_var_len_feature() const;
  public:
  void clear_var_len_feature();
  const ::tensorflow::VarLenFeatureProto& var_len_feature() const;
  PROTOBUF_NODISCARD ::tensorflow::VarLenFeatureProto* release_var_len_feature();
  ::tensorflow::VarLenFeatureProto* mutable_var_len_feature();
  void set_allocated_var_len_feature(::tensorflow::VarLenFeatureProto* var_len_feature);
  private:
  const ::tensorflow::VarLenFeatureProto& _internal_var_len_feature() const;
  ::tensorflow::VarLenFeatureProto* _internal_mutable_var_len_feature();
  public:
  void unsafe_arena_set_allocated_var_len_feature(
      ::tensorflow::VarLenFeatureProto* var_len_feature);
  ::tensorflow::VarLenFeatureProto* unsafe_arena_release_var_len_feature();

  void clear_config();
  ConfigCase config_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.FeatureConfiguration)
 private:
  class _Internal;
  void set_has_fixed_len_feature();
  void set_has_var_len_feature();

  inline bool has_config() const;
  inline void clear_has_config();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union ConfigUnion {
      constexpr ConfigUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::FixedLenFeatureProto* fixed_len_feature_;
      ::tensorflow::VarLenFeatureProto* var_len_feature_;
    } config_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class ExampleParserConfiguration_FeatureMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExampleParserConfiguration_FeatureMapEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureConfiguration,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExampleParserConfiguration_FeatureMapEntry_DoNotUse, 
    std::string, ::tensorflow::FeatureConfiguration,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ExampleParserConfiguration_FeatureMapEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ExampleParserConfiguration_FeatureMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ExampleParserConfiguration_FeatureMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExampleParserConfiguration_FeatureMapEntry_DoNotUse& other);
  static const ExampleParserConfiguration_FeatureMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExampleParserConfiguration_FeatureMapEntry_DoNotUse*>(&_ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ExampleParserConfiguration.FeatureMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};

// -------------------------------------------------------------------

class ExampleParserConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExampleParserConfiguration) */ {
 public:
  inline ExampleParserConfiguration() : ExampleParserConfiguration(nullptr) {}
  ~ExampleParserConfiguration() override;
  explicit PROTOBUF_CONSTEXPR ExampleParserConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExampleParserConfiguration(const ExampleParserConfiguration& from);
  ExampleParserConfiguration(ExampleParserConfiguration&& from) noexcept
    : ExampleParserConfiguration() {
    *this = ::std::move(from);
  }

  inline ExampleParserConfiguration& operator=(const ExampleParserConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExampleParserConfiguration& operator=(ExampleParserConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExampleParserConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExampleParserConfiguration* internal_default_instance() {
    return reinterpret_cast<const ExampleParserConfiguration*>(
               &_ExampleParserConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ExampleParserConfiguration& a, ExampleParserConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(ExampleParserConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExampleParserConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExampleParserConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExampleParserConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExampleParserConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExampleParserConfiguration& from) {
    ExampleParserConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExampleParserConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExampleParserConfiguration";
  }
  protected:
  explicit ExampleParserConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeatureMapFieldNumber = 1,
  };
  // map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
  int feature_map_size() const;
  private:
  int _internal_feature_map_size() const;
  public:
  void clear_feature_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >&
      _internal_feature_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >*
      _internal_mutable_feature_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >&
      feature_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >*
      mutable_feature_map();

  // @@protoc_insertion_point(class_scope:tensorflow.ExampleParserConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ExampleParserConfiguration_FeatureMapEntry_DoNotUse,
        std::string, ::tensorflow::FeatureConfiguration,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> feature_map_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VarLenFeatureProto

// .tensorflow.DataType dtype = 1;
inline void VarLenFeatureProto::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType VarLenFeatureProto::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType VarLenFeatureProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.dtype)
  return _internal_dtype();
}
inline void VarLenFeatureProto::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void VarLenFeatureProto::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.dtype)
}

// string values_output_tensor_name = 2;
inline void VarLenFeatureProto::clear_values_output_tensor_name() {
  _impl_.values_output_tensor_name_.ClearToEmpty();
}
inline const std::string& VarLenFeatureProto::values_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  return _internal_values_output_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VarLenFeatureProto::set_values_output_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.values_output_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}
inline std::string* VarLenFeatureProto::mutable_values_output_tensor_name() {
  std::string* _s = _internal_mutable_values_output_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  return _s;
}
inline const std::string& VarLenFeatureProto::_internal_values_output_tensor_name() const {
  return _impl_.values_output_tensor_name_.Get();
}
inline void VarLenFeatureProto::_internal_set_values_output_tensor_name(const std::string& value) {
  
  _impl_.values_output_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VarLenFeatureProto::_internal_mutable_values_output_tensor_name() {
  
  return _impl_.values_output_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VarLenFeatureProto::release_values_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VarLenFeatureProto.values_output_tensor_name)
  return _impl_.values_output_tensor_name_.Release();
}
inline void VarLenFeatureProto::set_allocated_values_output_tensor_name(std::string* values_output_tensor_name) {
  if (values_output_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.values_output_tensor_name_.SetAllocated(values_output_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.values_output_tensor_name_.IsDefault()) {
    _impl_.values_output_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VarLenFeatureProto.values_output_tensor_name)
}

// string indices_output_tensor_name = 3;
inline void VarLenFeatureProto::clear_indices_output_tensor_name() {
  _impl_.indices_output_tensor_name_.ClearToEmpty();
}
inline const std::string& VarLenFeatureProto::indices_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  return _internal_indices_output_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VarLenFeatureProto::set_indices_output_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.indices_output_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}
inline std::string* VarLenFeatureProto::mutable_indices_output_tensor_name() {
  std::string* _s = _internal_mutable_indices_output_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  return _s;
}
inline const std::string& VarLenFeatureProto::_internal_indices_output_tensor_name() const {
  return _impl_.indices_output_tensor_name_.Get();
}
inline void VarLenFeatureProto::_internal_set_indices_output_tensor_name(const std::string& value) {
  
  _impl_.indices_output_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VarLenFeatureProto::_internal_mutable_indices_output_tensor_name() {
  
  return _impl_.indices_output_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VarLenFeatureProto::release_indices_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
  return _impl_.indices_output_tensor_name_.Release();
}
inline void VarLenFeatureProto::set_allocated_indices_output_tensor_name(std::string* indices_output_tensor_name) {
  if (indices_output_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.indices_output_tensor_name_.SetAllocated(indices_output_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.indices_output_tensor_name_.IsDefault()) {
    _impl_.indices_output_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VarLenFeatureProto.indices_output_tensor_name)
}

// string shapes_output_tensor_name = 4;
inline void VarLenFeatureProto::clear_shapes_output_tensor_name() {
  _impl_.shapes_output_tensor_name_.ClearToEmpty();
}
inline const std::string& VarLenFeatureProto::shapes_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  return _internal_shapes_output_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VarLenFeatureProto::set_shapes_output_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.shapes_output_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}
inline std::string* VarLenFeatureProto::mutable_shapes_output_tensor_name() {
  std::string* _s = _internal_mutable_shapes_output_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  return _s;
}
inline const std::string& VarLenFeatureProto::_internal_shapes_output_tensor_name() const {
  return _impl_.shapes_output_tensor_name_.Get();
}
inline void VarLenFeatureProto::_internal_set_shapes_output_tensor_name(const std::string& value) {
  
  _impl_.shapes_output_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VarLenFeatureProto::_internal_mutable_shapes_output_tensor_name() {
  
  return _impl_.shapes_output_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VarLenFeatureProto::release_shapes_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
  return _impl_.shapes_output_tensor_name_.Release();
}
inline void VarLenFeatureProto::set_allocated_shapes_output_tensor_name(std::string* shapes_output_tensor_name) {
  if (shapes_output_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.shapes_output_tensor_name_.SetAllocated(shapes_output_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.shapes_output_tensor_name_.IsDefault()) {
    _impl_.shapes_output_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VarLenFeatureProto.shapes_output_tensor_name)
}

// -------------------------------------------------------------------

// FixedLenFeatureProto

// .tensorflow.DataType dtype = 1;
inline void FixedLenFeatureProto::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType FixedLenFeatureProto::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType FixedLenFeatureProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.dtype)
  return _internal_dtype();
}
inline void FixedLenFeatureProto::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void FixedLenFeatureProto::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.FixedLenFeatureProto.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool FixedLenFeatureProto::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool FixedLenFeatureProto::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& FixedLenFeatureProto::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& FixedLenFeatureProto::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.shape)
  return _internal_shape();
}
inline void FixedLenFeatureProto::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FixedLenFeatureProto.shape)
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.FixedLenFeatureProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* FixedLenFeatureProto::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.FixedLenFeatureProto.shape)
  return _msg;
}
inline void FixedLenFeatureProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FixedLenFeatureProto.shape)
}

// .tensorflow.TensorProto default_value = 3;
inline bool FixedLenFeatureProto::_internal_has_default_value() const {
  return this != internal_default_instance() && _impl_.default_value_ != nullptr;
}
inline bool FixedLenFeatureProto::has_default_value() const {
  return _internal_has_default_value();
}
inline const ::tensorflow::TensorProto& FixedLenFeatureProto::_internal_default_value() const {
  const ::tensorflow::TensorProto* p = _impl_.default_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& FixedLenFeatureProto::default_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.default_value)
  return _internal_default_value();
}
inline void FixedLenFeatureProto::unsafe_arena_set_allocated_default_value(
    ::tensorflow::TensorProto* default_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.default_value_);
  }
  _impl_.default_value_ = default_value;
  if (default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FixedLenFeatureProto.default_value)
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::release_default_value() {
  
  ::tensorflow::TensorProto* temp = _impl_.default_value_;
  _impl_.default_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.FixedLenFeatureProto.default_value)
  
  ::tensorflow::TensorProto* temp = _impl_.default_value_;
  _impl_.default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::_internal_mutable_default_value() {
  
  if (_impl_.default_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.default_value_ = p;
  }
  return _impl_.default_value_;
}
inline ::tensorflow::TensorProto* FixedLenFeatureProto::mutable_default_value() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_default_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.FixedLenFeatureProto.default_value)
  return _msg;
}
inline void FixedLenFeatureProto::set_allocated_default_value(::tensorflow::TensorProto* default_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.default_value_);
  }
  if (default_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value));
    if (message_arena != submessage_arena) {
      default_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FixedLenFeatureProto.default_value)
}

// string values_output_tensor_name = 4;
inline void FixedLenFeatureProto::clear_values_output_tensor_name() {
  _impl_.values_output_tensor_name_.ClearToEmpty();
}
inline const std::string& FixedLenFeatureProto::values_output_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  return _internal_values_output_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FixedLenFeatureProto::set_values_output_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.values_output_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}
inline std::string* FixedLenFeatureProto::mutable_values_output_tensor_name() {
  std::string* _s = _internal_mutable_values_output_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  return _s;
}
inline const std::string& FixedLenFeatureProto::_internal_values_output_tensor_name() const {
  return _impl_.values_output_tensor_name_.Get();
}
inline void FixedLenFeatureProto::_internal_set_values_output_tensor_name(const std::string& value) {
  
  _impl_.values_output_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* FixedLenFeatureProto::_internal_mutable_values_output_tensor_name() {
  
  return _impl_.values_output_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* FixedLenFeatureProto::release_values_output_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
  return _impl_.values_output_tensor_name_.Release();
}
inline void FixedLenFeatureProto::set_allocated_values_output_tensor_name(std::string* values_output_tensor_name) {
  if (values_output_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.values_output_tensor_name_.SetAllocated(values_output_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.values_output_tensor_name_.IsDefault()) {
    _impl_.values_output_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FixedLenFeatureProto.values_output_tensor_name)
}

// -------------------------------------------------------------------

// FeatureConfiguration

// .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
inline bool FeatureConfiguration::_internal_has_fixed_len_feature() const {
  return config_case() == kFixedLenFeature;
}
inline bool FeatureConfiguration::has_fixed_len_feature() const {
  return _internal_has_fixed_len_feature();
}
inline void FeatureConfiguration::set_has_fixed_len_feature() {
  _impl_._oneof_case_[0] = kFixedLenFeature;
}
inline void FeatureConfiguration::clear_fixed_len_feature() {
  if (_internal_has_fixed_len_feature()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.config_.fixed_len_feature_;
    }
    clear_has_config();
  }
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::release_fixed_len_feature() {
  // @@protoc_insertion_point(field_release:tensorflow.FeatureConfiguration.fixed_len_feature)
  if (_internal_has_fixed_len_feature()) {
    clear_has_config();
    ::tensorflow::FixedLenFeatureProto* temp = _impl_.config_.fixed_len_feature_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.config_.fixed_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::FixedLenFeatureProto& FeatureConfiguration::_internal_fixed_len_feature() const {
  return _internal_has_fixed_len_feature()
      ? *_impl_.config_.fixed_len_feature_
      : reinterpret_cast< ::tensorflow::FixedLenFeatureProto&>(::tensorflow::_FixedLenFeatureProto_default_instance_);
}
inline const ::tensorflow::FixedLenFeatureProto& FeatureConfiguration::fixed_len_feature() const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureConfiguration.fixed_len_feature)
  return _internal_fixed_len_feature();
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::unsafe_arena_release_fixed_len_feature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FeatureConfiguration.fixed_len_feature)
  if (_internal_has_fixed_len_feature()) {
    clear_has_config();
    ::tensorflow::FixedLenFeatureProto* temp = _impl_.config_.fixed_len_feature_;
    _impl_.config_.fixed_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void FeatureConfiguration::unsafe_arena_set_allocated_fixed_len_feature(::tensorflow::FixedLenFeatureProto* fixed_len_feature) {
  clear_config();
  if (fixed_len_feature) {
    set_has_fixed_len_feature();
    _impl_.config_.fixed_len_feature_ = fixed_len_feature;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FeatureConfiguration.fixed_len_feature)
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::_internal_mutable_fixed_len_feature() {
  if (!_internal_has_fixed_len_feature()) {
    clear_config();
    set_has_fixed_len_feature();
    _impl_.config_.fixed_len_feature_ = CreateMaybeMessage< ::tensorflow::FixedLenFeatureProto >(GetArenaForAllocation());
  }
  return _impl_.config_.fixed_len_feature_;
}
inline ::tensorflow::FixedLenFeatureProto* FeatureConfiguration::mutable_fixed_len_feature() {
  ::tensorflow::FixedLenFeatureProto* _msg = _internal_mutable_fixed_len_feature();
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureConfiguration.fixed_len_feature)
  return _msg;
}

// .tensorflow.VarLenFeatureProto var_len_feature = 2;
inline bool FeatureConfiguration::_internal_has_var_len_feature() const {
  return config_case() == kVarLenFeature;
}
inline bool FeatureConfiguration::has_var_len_feature() const {
  return _internal_has_var_len_feature();
}
inline void FeatureConfiguration::set_has_var_len_feature() {
  _impl_._oneof_case_[0] = kVarLenFeature;
}
inline void FeatureConfiguration::clear_var_len_feature() {
  if (_internal_has_var_len_feature()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.config_.var_len_feature_;
    }
    clear_has_config();
  }
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::release_var_len_feature() {
  // @@protoc_insertion_point(field_release:tensorflow.FeatureConfiguration.var_len_feature)
  if (_internal_has_var_len_feature()) {
    clear_has_config();
    ::tensorflow::VarLenFeatureProto* temp = _impl_.config_.var_len_feature_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.config_.var_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::VarLenFeatureProto& FeatureConfiguration::_internal_var_len_feature() const {
  return _internal_has_var_len_feature()
      ? *_impl_.config_.var_len_feature_
      : reinterpret_cast< ::tensorflow::VarLenFeatureProto&>(::tensorflow::_VarLenFeatureProto_default_instance_);
}
inline const ::tensorflow::VarLenFeatureProto& FeatureConfiguration::var_len_feature() const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureConfiguration.var_len_feature)
  return _internal_var_len_feature();
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::unsafe_arena_release_var_len_feature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FeatureConfiguration.var_len_feature)
  if (_internal_has_var_len_feature()) {
    clear_has_config();
    ::tensorflow::VarLenFeatureProto* temp = _impl_.config_.var_len_feature_;
    _impl_.config_.var_len_feature_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void FeatureConfiguration::unsafe_arena_set_allocated_var_len_feature(::tensorflow::VarLenFeatureProto* var_len_feature) {
  clear_config();
  if (var_len_feature) {
    set_has_var_len_feature();
    _impl_.config_.var_len_feature_ = var_len_feature;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FeatureConfiguration.var_len_feature)
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::_internal_mutable_var_len_feature() {
  if (!_internal_has_var_len_feature()) {
    clear_config();
    set_has_var_len_feature();
    _impl_.config_.var_len_feature_ = CreateMaybeMessage< ::tensorflow::VarLenFeatureProto >(GetArenaForAllocation());
  }
  return _impl_.config_.var_len_feature_;
}
inline ::tensorflow::VarLenFeatureProto* FeatureConfiguration::mutable_var_len_feature() {
  ::tensorflow::VarLenFeatureProto* _msg = _internal_mutable_var_len_feature();
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureConfiguration.var_len_feature)
  return _msg;
}

inline bool FeatureConfiguration::has_config() const {
  return config_case() != CONFIG_NOT_SET;
}
inline void FeatureConfiguration::clear_has_config() {
  _impl_._oneof_case_[0] = CONFIG_NOT_SET;
}
inline FeatureConfiguration::ConfigCase FeatureConfiguration::config_case() const {
  return FeatureConfiguration::ConfigCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExampleParserConfiguration

// map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
inline int ExampleParserConfiguration::_internal_feature_map_size() const {
  return _impl_.feature_map_.size();
}
inline int ExampleParserConfiguration::feature_map_size() const {
  return _internal_feature_map_size();
}
inline void ExampleParserConfiguration::clear_feature_map() {
  _impl_.feature_map_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >&
ExampleParserConfiguration::_internal_feature_map() const {
  return _impl_.feature_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >&
ExampleParserConfiguration::feature_map() const {
  // @@protoc_insertion_point(field_map:tensorflow.ExampleParserConfiguration.feature_map)
  return _internal_feature_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >*
ExampleParserConfiguration::_internal_mutable_feature_map() {
  return _impl_.feature_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::FeatureConfiguration >*
ExampleParserConfiguration::mutable_feature_map() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ExampleParserConfiguration.feature_map)
  return _internal_mutable_feature_map();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
