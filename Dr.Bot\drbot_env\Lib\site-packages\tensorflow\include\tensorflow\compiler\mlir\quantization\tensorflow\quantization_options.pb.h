// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/quantization/tensorflow/quantization_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
namespace tensorflow {
namespace quantization {
class FreezeAllVariables;
struct FreezeAllVariablesDefaultTypeInternal;
extern FreezeAllVariablesDefaultTypeInternal _FreezeAllVariables_default_instance_;
class QuantizationMethod;
struct QuantizationMethodDefaultTypeInternal;
extern QuantizationMethodDefaultTypeInternal _QuantizationMethod_default_instance_;
class QuantizationOptions;
struct QuantizationOptionsDefaultTypeInternal;
extern QuantizationOptionsDefaultTypeInternal _QuantizationOptions_default_instance_;
class UnitWiseQuantizationPrecision;
struct UnitWiseQuantizationPrecisionDefaultTypeInternal;
extern UnitWiseQuantizationPrecisionDefaultTypeInternal _UnitWiseQuantizationPrecision_default_instance_;
}  // namespace quantization
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::quantization::FreezeAllVariables* Arena::CreateMaybeMessage<::tensorflow::quantization::FreezeAllVariables>(Arena*);
template<> ::tensorflow::quantization::QuantizationMethod* Arena::CreateMaybeMessage<::tensorflow::quantization::QuantizationMethod>(Arena*);
template<> ::tensorflow::quantization::QuantizationOptions* Arena::CreateMaybeMessage<::tensorflow::quantization::QuantizationOptions>(Arena*);
template<> ::tensorflow::quantization::UnitWiseQuantizationPrecision* Arena::CreateMaybeMessage<::tensorflow::quantization::UnitWiseQuantizationPrecision>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace quantization {

enum QuantizationMethod_Method : int {
  QuantizationMethod_Method_METHOD_UNSPECIFIED = 0,
  QuantizationMethod_Method_QuantizationMethod_Method_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationMethod_Method_QuantizationMethod_Method_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationMethod_Method_IsValid(int value);
constexpr QuantizationMethod_Method QuantizationMethod_Method_Method_MIN = QuantizationMethod_Method_METHOD_UNSPECIFIED;
constexpr QuantizationMethod_Method QuantizationMethod_Method_Method_MAX = QuantizationMethod_Method_METHOD_UNSPECIFIED;
constexpr int QuantizationMethod_Method_Method_ARRAYSIZE = QuantizationMethod_Method_Method_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationMethod_Method_descriptor();
template<typename T>
inline const std::string& QuantizationMethod_Method_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationMethod_Method>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationMethod_Method_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationMethod_Method_descriptor(), enum_t_value);
}
inline bool QuantizationMethod_Method_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationMethod_Method* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationMethod_Method>(
    QuantizationMethod_Method_descriptor(), name, value);
}
enum QuantizationMethod_ExperimentalMethod : int {
  QuantizationMethod_ExperimentalMethod_EXPERIMENTAL_METHOD_UNSPECIFIED = 0,
  QuantizationMethod_ExperimentalMethod_STATIC_RANGE = 1,
  QuantizationMethod_ExperimentalMethod_DYNAMIC_RANGE = 2,
  QuantizationMethod_ExperimentalMethod_WEIGHT_ONLY = 3,
  QuantizationMethod_ExperimentalMethod_QuantizationMethod_ExperimentalMethod_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationMethod_ExperimentalMethod_QuantizationMethod_ExperimentalMethod_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationMethod_ExperimentalMethod_IsValid(int value);
constexpr QuantizationMethod_ExperimentalMethod QuantizationMethod_ExperimentalMethod_ExperimentalMethod_MIN = QuantizationMethod_ExperimentalMethod_EXPERIMENTAL_METHOD_UNSPECIFIED;
constexpr QuantizationMethod_ExperimentalMethod QuantizationMethod_ExperimentalMethod_ExperimentalMethod_MAX = QuantizationMethod_ExperimentalMethod_WEIGHT_ONLY;
constexpr int QuantizationMethod_ExperimentalMethod_ExperimentalMethod_ARRAYSIZE = QuantizationMethod_ExperimentalMethod_ExperimentalMethod_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationMethod_ExperimentalMethod_descriptor();
template<typename T>
inline const std::string& QuantizationMethod_ExperimentalMethod_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationMethod_ExperimentalMethod>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationMethod_ExperimentalMethod_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationMethod_ExperimentalMethod_descriptor(), enum_t_value);
}
inline bool QuantizationMethod_ExperimentalMethod_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationMethod_ExperimentalMethod* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationMethod_ExperimentalMethod>(
    QuantizationMethod_ExperimentalMethod_descriptor(), name, value);
}
enum UnitWiseQuantizationPrecision_UnitType : int {
  UnitWiseQuantizationPrecision_UnitType_UNIT_UNSPECIFIED = 0,
  UnitWiseQuantizationPrecision_UnitType_UNIT_NODE = 1,
  UnitWiseQuantizationPrecision_UnitType_UNIT_OP = 2,
  UnitWiseQuantizationPrecision_UnitType_UnitWiseQuantizationPrecision_UnitType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  UnitWiseQuantizationPrecision_UnitType_UnitWiseQuantizationPrecision_UnitType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool UnitWiseQuantizationPrecision_UnitType_IsValid(int value);
constexpr UnitWiseQuantizationPrecision_UnitType UnitWiseQuantizationPrecision_UnitType_UnitType_MIN = UnitWiseQuantizationPrecision_UnitType_UNIT_UNSPECIFIED;
constexpr UnitWiseQuantizationPrecision_UnitType UnitWiseQuantizationPrecision_UnitType_UnitType_MAX = UnitWiseQuantizationPrecision_UnitType_UNIT_OP;
constexpr int UnitWiseQuantizationPrecision_UnitType_UnitType_ARRAYSIZE = UnitWiseQuantizationPrecision_UnitType_UnitType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* UnitWiseQuantizationPrecision_UnitType_descriptor();
template<typename T>
inline const std::string& UnitWiseQuantizationPrecision_UnitType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, UnitWiseQuantizationPrecision_UnitType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function UnitWiseQuantizationPrecision_UnitType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    UnitWiseQuantizationPrecision_UnitType_descriptor(), enum_t_value);
}
inline bool UnitWiseQuantizationPrecision_UnitType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, UnitWiseQuantizationPrecision_UnitType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<UnitWiseQuantizationPrecision_UnitType>(
    UnitWiseQuantizationPrecision_UnitType_descriptor(), name, value);
}
enum QuantizationPrecision : int {
  PRECISION_UNSPECIFIED = 0,
  PRECISION_FULL = 1,
  PRECISION_W4A4 = 2,
  PRECISION_W4A8 = 3,
  PRECISION_W8A8 = 4,
  QuantizationPrecision_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  QuantizationPrecision_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool QuantizationPrecision_IsValid(int value);
constexpr QuantizationPrecision QuantizationPrecision_MIN = PRECISION_UNSPECIFIED;
constexpr QuantizationPrecision QuantizationPrecision_MAX = PRECISION_W8A8;
constexpr int QuantizationPrecision_ARRAYSIZE = QuantizationPrecision_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* QuantizationPrecision_descriptor();
template<typename T>
inline const std::string& QuantizationPrecision_Name(T enum_t_value) {
  static_assert(::std::is_same<T, QuantizationPrecision>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function QuantizationPrecision_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    QuantizationPrecision_descriptor(), enum_t_value);
}
inline bool QuantizationPrecision_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, QuantizationPrecision* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<QuantizationPrecision>(
    QuantizationPrecision_descriptor(), name, value);
}
enum OpSet : int {
  OP_SET_UNSPECIFIED = 0,
  TF = 1,
  XLA = 2,
  UNIFORM_QUANTIZED = 3,
  OpSet_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OpSet_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OpSet_IsValid(int value);
constexpr OpSet OpSet_MIN = OP_SET_UNSPECIFIED;
constexpr OpSet OpSet_MAX = UNIFORM_QUANTIZED;
constexpr int OpSet_ARRAYSIZE = OpSet_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OpSet_descriptor();
template<typename T>
inline const std::string& OpSet_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OpSet>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OpSet_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OpSet_descriptor(), enum_t_value);
}
inline bool OpSet_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OpSet* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OpSet>(
    OpSet_descriptor(), name, value);
}
// ===================================================================

class QuantizationMethod final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.QuantizationMethod) */ {
 public:
  inline QuantizationMethod() : QuantizationMethod(nullptr) {}
  ~QuantizationMethod() override;
  explicit PROTOBUF_CONSTEXPR QuantizationMethod(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationMethod(const QuantizationMethod& from);
  QuantizationMethod(QuantizationMethod&& from) noexcept
    : QuantizationMethod() {
    *this = ::std::move(from);
  }

  inline QuantizationMethod& operator=(const QuantizationMethod& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationMethod& operator=(QuantizationMethod&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationMethod& default_instance() {
    return *internal_default_instance();
  }
  enum MethodOneofCase {
    kMethod = 1,
    kExperimentalMethod = 2,
    METHOD_ONEOF_NOT_SET = 0,
  };

  static inline const QuantizationMethod* internal_default_instance() {
    return reinterpret_cast<const QuantizationMethod*>(
               &_QuantizationMethod_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(QuantizationMethod& a, QuantizationMethod& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationMethod* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationMethod* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationMethod* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationMethod>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationMethod& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationMethod& from) {
    QuantizationMethod::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationMethod* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.QuantizationMethod";
  }
  protected:
  explicit QuantizationMethod(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef QuantizationMethod_Method Method;
  static constexpr Method METHOD_UNSPECIFIED =
    QuantizationMethod_Method_METHOD_UNSPECIFIED;
  static inline bool Method_IsValid(int value) {
    return QuantizationMethod_Method_IsValid(value);
  }
  static constexpr Method Method_MIN =
    QuantizationMethod_Method_Method_MIN;
  static constexpr Method Method_MAX =
    QuantizationMethod_Method_Method_MAX;
  static constexpr int Method_ARRAYSIZE =
    QuantizationMethod_Method_Method_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Method_descriptor() {
    return QuantizationMethod_Method_descriptor();
  }
  template<typename T>
  static inline const std::string& Method_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Method>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Method_Name.");
    return QuantizationMethod_Method_Name(enum_t_value);
  }
  static inline bool Method_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Method* value) {
    return QuantizationMethod_Method_Parse(name, value);
  }

  typedef QuantizationMethod_ExperimentalMethod ExperimentalMethod;
  static constexpr ExperimentalMethod EXPERIMENTAL_METHOD_UNSPECIFIED =
    QuantizationMethod_ExperimentalMethod_EXPERIMENTAL_METHOD_UNSPECIFIED;
  static constexpr ExperimentalMethod STATIC_RANGE =
    QuantizationMethod_ExperimentalMethod_STATIC_RANGE;
  static constexpr ExperimentalMethod DYNAMIC_RANGE =
    QuantizationMethod_ExperimentalMethod_DYNAMIC_RANGE;
  static constexpr ExperimentalMethod WEIGHT_ONLY =
    QuantizationMethod_ExperimentalMethod_WEIGHT_ONLY;
  static inline bool ExperimentalMethod_IsValid(int value) {
    return QuantizationMethod_ExperimentalMethod_IsValid(value);
  }
  static constexpr ExperimentalMethod ExperimentalMethod_MIN =
    QuantizationMethod_ExperimentalMethod_ExperimentalMethod_MIN;
  static constexpr ExperimentalMethod ExperimentalMethod_MAX =
    QuantizationMethod_ExperimentalMethod_ExperimentalMethod_MAX;
  static constexpr int ExperimentalMethod_ARRAYSIZE =
    QuantizationMethod_ExperimentalMethod_ExperimentalMethod_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ExperimentalMethod_descriptor() {
    return QuantizationMethod_ExperimentalMethod_descriptor();
  }
  template<typename T>
  static inline const std::string& ExperimentalMethod_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ExperimentalMethod>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ExperimentalMethod_Name.");
    return QuantizationMethod_ExperimentalMethod_Name(enum_t_value);
  }
  static inline bool ExperimentalMethod_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ExperimentalMethod* value) {
    return QuantizationMethod_ExperimentalMethod_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kMethodFieldNumber = 1,
    kExperimentalMethodFieldNumber = 2,
  };
  // .tensorflow.quantization.QuantizationMethod.Method method = 1;
  bool has_method() const;
  private:
  bool _internal_has_method() const;
  public:
  void clear_method();
  ::tensorflow::quantization::QuantizationMethod_Method method() const;
  void set_method(::tensorflow::quantization::QuantizationMethod_Method value);
  private:
  ::tensorflow::quantization::QuantizationMethod_Method _internal_method() const;
  void _internal_set_method(::tensorflow::quantization::QuantizationMethod_Method value);
  public:

  // .tensorflow.quantization.QuantizationMethod.ExperimentalMethod experimental_method = 2;
  bool has_experimental_method() const;
  private:
  bool _internal_has_experimental_method() const;
  public:
  void clear_experimental_method();
  ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod experimental_method() const;
  void set_experimental_method(::tensorflow::quantization::QuantizationMethod_ExperimentalMethod value);
  private:
  ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod _internal_experimental_method() const;
  void _internal_set_experimental_method(::tensorflow::quantization::QuantizationMethod_ExperimentalMethod value);
  public:

  void clear_method_oneof();
  MethodOneofCase method_oneof_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.quantization.QuantizationMethod)
 private:
  class _Internal;
  void set_has_method();
  void set_has_experimental_method();

  inline bool has_method_oneof() const;
  inline void clear_has_method_oneof();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union MethodOneofUnion {
      constexpr MethodOneofUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int method_;
      int experimental_method_;
    } method_oneof_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class UnitWiseQuantizationPrecision final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.UnitWiseQuantizationPrecision) */ {
 public:
  inline UnitWiseQuantizationPrecision() : UnitWiseQuantizationPrecision(nullptr) {}
  ~UnitWiseQuantizationPrecision() override;
  explicit PROTOBUF_CONSTEXPR UnitWiseQuantizationPrecision(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UnitWiseQuantizationPrecision(const UnitWiseQuantizationPrecision& from);
  UnitWiseQuantizationPrecision(UnitWiseQuantizationPrecision&& from) noexcept
    : UnitWiseQuantizationPrecision() {
    *this = ::std::move(from);
  }

  inline UnitWiseQuantizationPrecision& operator=(const UnitWiseQuantizationPrecision& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnitWiseQuantizationPrecision& operator=(UnitWiseQuantizationPrecision&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UnitWiseQuantizationPrecision& default_instance() {
    return *internal_default_instance();
  }
  static inline const UnitWiseQuantizationPrecision* internal_default_instance() {
    return reinterpret_cast<const UnitWiseQuantizationPrecision*>(
               &_UnitWiseQuantizationPrecision_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(UnitWiseQuantizationPrecision& a, UnitWiseQuantizationPrecision& b) {
    a.Swap(&b);
  }
  inline void Swap(UnitWiseQuantizationPrecision* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UnitWiseQuantizationPrecision* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UnitWiseQuantizationPrecision* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UnitWiseQuantizationPrecision>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UnitWiseQuantizationPrecision& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UnitWiseQuantizationPrecision& from) {
    UnitWiseQuantizationPrecision::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnitWiseQuantizationPrecision* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.UnitWiseQuantizationPrecision";
  }
  protected:
  explicit UnitWiseQuantizationPrecision(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef UnitWiseQuantizationPrecision_UnitType UnitType;
  static constexpr UnitType UNIT_UNSPECIFIED =
    UnitWiseQuantizationPrecision_UnitType_UNIT_UNSPECIFIED;
  static constexpr UnitType UNIT_NODE =
    UnitWiseQuantizationPrecision_UnitType_UNIT_NODE;
  static constexpr UnitType UNIT_OP =
    UnitWiseQuantizationPrecision_UnitType_UNIT_OP;
  static inline bool UnitType_IsValid(int value) {
    return UnitWiseQuantizationPrecision_UnitType_IsValid(value);
  }
  static constexpr UnitType UnitType_MIN =
    UnitWiseQuantizationPrecision_UnitType_UnitType_MIN;
  static constexpr UnitType UnitType_MAX =
    UnitWiseQuantizationPrecision_UnitType_UnitType_MAX;
  static constexpr int UnitType_ARRAYSIZE =
    UnitWiseQuantizationPrecision_UnitType_UnitType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  UnitType_descriptor() {
    return UnitWiseQuantizationPrecision_UnitType_descriptor();
  }
  template<typename T>
  static inline const std::string& UnitType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, UnitType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function UnitType_Name.");
    return UnitWiseQuantizationPrecision_UnitType_Name(enum_t_value);
  }
  static inline bool UnitType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      UnitType* value) {
    return UnitWiseQuantizationPrecision_UnitType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFuncNameFieldNumber = 2,
    kUnitNameFieldNumber = 3,
    kUnitTypeFieldNumber = 1,
    kQuantizationPrecisionFieldNumber = 5,
  };
  // string func_name = 2;
  void clear_func_name();
  const std::string& func_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_func_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_func_name();
  PROTOBUF_NODISCARD std::string* release_func_name();
  void set_allocated_func_name(std::string* func_name);
  private:
  const std::string& _internal_func_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_func_name(const std::string& value);
  std::string* _internal_mutable_func_name();
  public:

  // string unit_name = 3;
  void clear_unit_name();
  const std::string& unit_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_unit_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_unit_name();
  PROTOBUF_NODISCARD std::string* release_unit_name();
  void set_allocated_unit_name(std::string* unit_name);
  private:
  const std::string& _internal_unit_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_unit_name(const std::string& value);
  std::string* _internal_mutable_unit_name();
  public:

  // .tensorflow.quantization.UnitWiseQuantizationPrecision.UnitType unit_type = 1;
  void clear_unit_type();
  ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType unit_type() const;
  void set_unit_type(::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType value);
  private:
  ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType _internal_unit_type() const;
  void _internal_set_unit_type(::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType value);
  public:

  // .tensorflow.quantization.QuantizationPrecision quantization_precision = 5;
  void clear_quantization_precision();
  ::tensorflow::quantization::QuantizationPrecision quantization_precision() const;
  void set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value);
  private:
  ::tensorflow::quantization::QuantizationPrecision _internal_quantization_precision() const;
  void _internal_set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.UnitWiseQuantizationPrecision)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr func_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr unit_name_;
    int unit_type_;
    int quantization_precision_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class FreezeAllVariables final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.FreezeAllVariables) */ {
 public:
  inline FreezeAllVariables() : FreezeAllVariables(nullptr) {}
  ~FreezeAllVariables() override;
  explicit PROTOBUF_CONSTEXPR FreezeAllVariables(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FreezeAllVariables(const FreezeAllVariables& from);
  FreezeAllVariables(FreezeAllVariables&& from) noexcept
    : FreezeAllVariables() {
    *this = ::std::move(from);
  }

  inline FreezeAllVariables& operator=(const FreezeAllVariables& from) {
    CopyFrom(from);
    return *this;
  }
  inline FreezeAllVariables& operator=(FreezeAllVariables&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FreezeAllVariables& default_instance() {
    return *internal_default_instance();
  }
  static inline const FreezeAllVariables* internal_default_instance() {
    return reinterpret_cast<const FreezeAllVariables*>(
               &_FreezeAllVariables_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FreezeAllVariables& a, FreezeAllVariables& b) {
    a.Swap(&b);
  }
  inline void Swap(FreezeAllVariables* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FreezeAllVariables* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FreezeAllVariables* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FreezeAllVariables>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FreezeAllVariables& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FreezeAllVariables& from) {
    FreezeAllVariables::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FreezeAllVariables* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.FreezeAllVariables";
  }
  protected:
  explicit FreezeAllVariables(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.FreezeAllVariables)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool enabled_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// -------------------------------------------------------------------

class QuantizationOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.QuantizationOptions) */ {
 public:
  inline QuantizationOptions() : QuantizationOptions(nullptr) {}
  ~QuantizationOptions() override;
  explicit PROTOBUF_CONSTEXPR QuantizationOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationOptions(const QuantizationOptions& from);
  QuantizationOptions(QuantizationOptions&& from) noexcept
    : QuantizationOptions() {
    *this = ::std::move(from);
  }

  inline QuantizationOptions& operator=(const QuantizationOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationOptions& operator=(QuantizationOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationOptions* internal_default_instance() {
    return reinterpret_cast<const QuantizationOptions*>(
               &_QuantizationOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(QuantizationOptions& a, QuantizationOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationOptions& from) {
    QuantizationOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.QuantizationOptions";
  }
  protected:
  explicit QuantizationOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUnitWiseQuantizationPrecisionFieldNumber = 4,
    kQuantizationMethodFieldNumber = 1,
    kFreezeAllVariablesFieldNumber = 6,
    kOpSetFieldNumber = 2,
    kQuantizationPrecisionFieldNumber = 3,
    kMinNumElementsForWeightsFieldNumber = 5,
    kEnablePerChannelQuantizationFieldNumber = 7,
    kEnableTwoInputTensorsFieldNumber = 8,
  };
  // repeated .tensorflow.quantization.UnitWiseQuantizationPrecision unit_wise_quantization_precision = 4;
  int unit_wise_quantization_precision_size() const;
  private:
  int _internal_unit_wise_quantization_precision_size() const;
  public:
  void clear_unit_wise_quantization_precision();
  ::tensorflow::quantization::UnitWiseQuantizationPrecision* mutable_unit_wise_quantization_precision(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationPrecision >*
      mutable_unit_wise_quantization_precision();
  private:
  const ::tensorflow::quantization::UnitWiseQuantizationPrecision& _internal_unit_wise_quantization_precision(int index) const;
  ::tensorflow::quantization::UnitWiseQuantizationPrecision* _internal_add_unit_wise_quantization_precision();
  public:
  const ::tensorflow::quantization::UnitWiseQuantizationPrecision& unit_wise_quantization_precision(int index) const;
  ::tensorflow::quantization::UnitWiseQuantizationPrecision* add_unit_wise_quantization_precision();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationPrecision >&
      unit_wise_quantization_precision() const;

  // .tensorflow.quantization.QuantizationMethod quantization_method = 1;
  bool has_quantization_method() const;
  private:
  bool _internal_has_quantization_method() const;
  public:
  void clear_quantization_method();
  const ::tensorflow::quantization::QuantizationMethod& quantization_method() const;
  PROTOBUF_NODISCARD ::tensorflow::quantization::QuantizationMethod* release_quantization_method();
  ::tensorflow::quantization::QuantizationMethod* mutable_quantization_method();
  void set_allocated_quantization_method(::tensorflow::quantization::QuantizationMethod* quantization_method);
  private:
  const ::tensorflow::quantization::QuantizationMethod& _internal_quantization_method() const;
  ::tensorflow::quantization::QuantizationMethod* _internal_mutable_quantization_method();
  public:
  void unsafe_arena_set_allocated_quantization_method(
      ::tensorflow::quantization::QuantizationMethod* quantization_method);
  ::tensorflow::quantization::QuantizationMethod* unsafe_arena_release_quantization_method();

  // .tensorflow.quantization.FreezeAllVariables freeze_all_variables = 6;
  bool has_freeze_all_variables() const;
  private:
  bool _internal_has_freeze_all_variables() const;
  public:
  void clear_freeze_all_variables();
  const ::tensorflow::quantization::FreezeAllVariables& freeze_all_variables() const;
  PROTOBUF_NODISCARD ::tensorflow::quantization::FreezeAllVariables* release_freeze_all_variables();
  ::tensorflow::quantization::FreezeAllVariables* mutable_freeze_all_variables();
  void set_allocated_freeze_all_variables(::tensorflow::quantization::FreezeAllVariables* freeze_all_variables);
  private:
  const ::tensorflow::quantization::FreezeAllVariables& _internal_freeze_all_variables() const;
  ::tensorflow::quantization::FreezeAllVariables* _internal_mutable_freeze_all_variables();
  public:
  void unsafe_arena_set_allocated_freeze_all_variables(
      ::tensorflow::quantization::FreezeAllVariables* freeze_all_variables);
  ::tensorflow::quantization::FreezeAllVariables* unsafe_arena_release_freeze_all_variables();

  // .tensorflow.quantization.OpSet op_set = 2;
  void clear_op_set();
  ::tensorflow::quantization::OpSet op_set() const;
  void set_op_set(::tensorflow::quantization::OpSet value);
  private:
  ::tensorflow::quantization::OpSet _internal_op_set() const;
  void _internal_set_op_set(::tensorflow::quantization::OpSet value);
  public:

  // .tensorflow.quantization.QuantizationPrecision quantization_precision = 3;
  void clear_quantization_precision();
  ::tensorflow::quantization::QuantizationPrecision quantization_precision() const;
  void set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value);
  private:
  ::tensorflow::quantization::QuantizationPrecision _internal_quantization_precision() const;
  void _internal_set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value);
  public:

  // int64 min_num_elements_for_weights = 5;
  void clear_min_num_elements_for_weights();
  int64_t min_num_elements_for_weights() const;
  void set_min_num_elements_for_weights(int64_t value);
  private:
  int64_t _internal_min_num_elements_for_weights() const;
  void _internal_set_min_num_elements_for_weights(int64_t value);
  public:

  // bool enable_per_channel_quantization = 7;
  void clear_enable_per_channel_quantization();
  bool enable_per_channel_quantization() const;
  void set_enable_per_channel_quantization(bool value);
  private:
  bool _internal_enable_per_channel_quantization() const;
  void _internal_set_enable_per_channel_quantization(bool value);
  public:

  // bool enable_two_input_tensors = 8;
  void clear_enable_two_input_tensors();
  bool enable_two_input_tensors() const;
  void set_enable_two_input_tensors(bool value);
  private:
  bool _internal_enable_two_input_tensors() const;
  void _internal_set_enable_two_input_tensors(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.QuantizationOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationPrecision > unit_wise_quantization_precision_;
    ::tensorflow::quantization::QuantizationMethod* quantization_method_;
    ::tensorflow::quantization::FreezeAllVariables* freeze_all_variables_;
    int op_set_;
    int quantization_precision_;
    int64_t min_num_elements_for_weights_;
    bool enable_per_channel_quantization_;
    bool enable_two_input_tensors_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// QuantizationMethod

// .tensorflow.quantization.QuantizationMethod.Method method = 1;
inline bool QuantizationMethod::_internal_has_method() const {
  return method_oneof_case() == kMethod;
}
inline bool QuantizationMethod::has_method() const {
  return _internal_has_method();
}
inline void QuantizationMethod::set_has_method() {
  _impl_._oneof_case_[0] = kMethod;
}
inline void QuantizationMethod::clear_method() {
  if (_internal_has_method()) {
    _impl_.method_oneof_.method_ = 0;
    clear_has_method_oneof();
  }
}
inline ::tensorflow::quantization::QuantizationMethod_Method QuantizationMethod::_internal_method() const {
  if (_internal_has_method()) {
    return static_cast< ::tensorflow::quantization::QuantizationMethod_Method >(_impl_.method_oneof_.method_);
  }
  return static_cast< ::tensorflow::quantization::QuantizationMethod_Method >(0);
}
inline ::tensorflow::quantization::QuantizationMethod_Method QuantizationMethod::method() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationMethod.method)
  return _internal_method();
}
inline void QuantizationMethod::_internal_set_method(::tensorflow::quantization::QuantizationMethod_Method value) {
  if (!_internal_has_method()) {
    clear_method_oneof();
    set_has_method();
  }
  _impl_.method_oneof_.method_ = value;
}
inline void QuantizationMethod::set_method(::tensorflow::quantization::QuantizationMethod_Method value) {
  _internal_set_method(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationMethod.method)
}

// .tensorflow.quantization.QuantizationMethod.ExperimentalMethod experimental_method = 2;
inline bool QuantizationMethod::_internal_has_experimental_method() const {
  return method_oneof_case() == kExperimentalMethod;
}
inline bool QuantizationMethod::has_experimental_method() const {
  return _internal_has_experimental_method();
}
inline void QuantizationMethod::set_has_experimental_method() {
  _impl_._oneof_case_[0] = kExperimentalMethod;
}
inline void QuantizationMethod::clear_experimental_method() {
  if (_internal_has_experimental_method()) {
    _impl_.method_oneof_.experimental_method_ = 0;
    clear_has_method_oneof();
  }
}
inline ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod QuantizationMethod::_internal_experimental_method() const {
  if (_internal_has_experimental_method()) {
    return static_cast< ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod >(_impl_.method_oneof_.experimental_method_);
  }
  return static_cast< ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod >(0);
}
inline ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod QuantizationMethod::experimental_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationMethod.experimental_method)
  return _internal_experimental_method();
}
inline void QuantizationMethod::_internal_set_experimental_method(::tensorflow::quantization::QuantizationMethod_ExperimentalMethod value) {
  if (!_internal_has_experimental_method()) {
    clear_method_oneof();
    set_has_experimental_method();
  }
  _impl_.method_oneof_.experimental_method_ = value;
}
inline void QuantizationMethod::set_experimental_method(::tensorflow::quantization::QuantizationMethod_ExperimentalMethod value) {
  _internal_set_experimental_method(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationMethod.experimental_method)
}

inline bool QuantizationMethod::has_method_oneof() const {
  return method_oneof_case() != METHOD_ONEOF_NOT_SET;
}
inline void QuantizationMethod::clear_has_method_oneof() {
  _impl_._oneof_case_[0] = METHOD_ONEOF_NOT_SET;
}
inline QuantizationMethod::MethodOneofCase QuantizationMethod::method_oneof_case() const {
  return QuantizationMethod::MethodOneofCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// UnitWiseQuantizationPrecision

// .tensorflow.quantization.UnitWiseQuantizationPrecision.UnitType unit_type = 1;
inline void UnitWiseQuantizationPrecision::clear_unit_type() {
  _impl_.unit_type_ = 0;
}
inline ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType UnitWiseQuantizationPrecision::_internal_unit_type() const {
  return static_cast< ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType >(_impl_.unit_type_);
}
inline ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType UnitWiseQuantizationPrecision::unit_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_type)
  return _internal_unit_type();
}
inline void UnitWiseQuantizationPrecision::_internal_set_unit_type(::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType value) {
  
  _impl_.unit_type_ = value;
}
inline void UnitWiseQuantizationPrecision::set_unit_type(::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType value) {
  _internal_set_unit_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_type)
}

// string func_name = 2;
inline void UnitWiseQuantizationPrecision::clear_func_name() {
  _impl_.func_name_.ClearToEmpty();
}
inline const std::string& UnitWiseQuantizationPrecision::func_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationPrecision.func_name)
  return _internal_func_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnitWiseQuantizationPrecision::set_func_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.func_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationPrecision.func_name)
}
inline std::string* UnitWiseQuantizationPrecision::mutable_func_name() {
  std::string* _s = _internal_mutable_func_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationPrecision.func_name)
  return _s;
}
inline const std::string& UnitWiseQuantizationPrecision::_internal_func_name() const {
  return _impl_.func_name_.Get();
}
inline void UnitWiseQuantizationPrecision::_internal_set_func_name(const std::string& value) {
  
  _impl_.func_name_.Set(value, GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationPrecision::_internal_mutable_func_name() {
  
  return _impl_.func_name_.Mutable(GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationPrecision::release_func_name() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.UnitWiseQuantizationPrecision.func_name)
  return _impl_.func_name_.Release();
}
inline void UnitWiseQuantizationPrecision::set_allocated_func_name(std::string* func_name) {
  if (func_name != nullptr) {
    
  } else {
    
  }
  _impl_.func_name_.SetAllocated(func_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.func_name_.IsDefault()) {
    _impl_.func_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.UnitWiseQuantizationPrecision.func_name)
}

// string unit_name = 3;
inline void UnitWiseQuantizationPrecision::clear_unit_name() {
  _impl_.unit_name_.ClearToEmpty();
}
inline const std::string& UnitWiseQuantizationPrecision::unit_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_name)
  return _internal_unit_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnitWiseQuantizationPrecision::set_unit_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.unit_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_name)
}
inline std::string* UnitWiseQuantizationPrecision::mutable_unit_name() {
  std::string* _s = _internal_mutable_unit_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_name)
  return _s;
}
inline const std::string& UnitWiseQuantizationPrecision::_internal_unit_name() const {
  return _impl_.unit_name_.Get();
}
inline void UnitWiseQuantizationPrecision::_internal_set_unit_name(const std::string& value) {
  
  _impl_.unit_name_.Set(value, GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationPrecision::_internal_mutable_unit_name() {
  
  return _impl_.unit_name_.Mutable(GetArenaForAllocation());
}
inline std::string* UnitWiseQuantizationPrecision::release_unit_name() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_name)
  return _impl_.unit_name_.Release();
}
inline void UnitWiseQuantizationPrecision::set_allocated_unit_name(std::string* unit_name) {
  if (unit_name != nullptr) {
    
  } else {
    
  }
  _impl_.unit_name_.SetAllocated(unit_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.unit_name_.IsDefault()) {
    _impl_.unit_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.UnitWiseQuantizationPrecision.unit_name)
}

// .tensorflow.quantization.QuantizationPrecision quantization_precision = 5;
inline void UnitWiseQuantizationPrecision::clear_quantization_precision() {
  _impl_.quantization_precision_ = 0;
}
inline ::tensorflow::quantization::QuantizationPrecision UnitWiseQuantizationPrecision::_internal_quantization_precision() const {
  return static_cast< ::tensorflow::quantization::QuantizationPrecision >(_impl_.quantization_precision_);
}
inline ::tensorflow::quantization::QuantizationPrecision UnitWiseQuantizationPrecision::quantization_precision() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.UnitWiseQuantizationPrecision.quantization_precision)
  return _internal_quantization_precision();
}
inline void UnitWiseQuantizationPrecision::_internal_set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value) {
  
  _impl_.quantization_precision_ = value;
}
inline void UnitWiseQuantizationPrecision::set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value) {
  _internal_set_quantization_precision(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.UnitWiseQuantizationPrecision.quantization_precision)
}

// -------------------------------------------------------------------

// FreezeAllVariables

// bool enabled = 1;
inline void FreezeAllVariables::clear_enabled() {
  _impl_.enabled_ = false;
}
inline bool FreezeAllVariables::_internal_enabled() const {
  return _impl_.enabled_;
}
inline bool FreezeAllVariables::enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.FreezeAllVariables.enabled)
  return _internal_enabled();
}
inline void FreezeAllVariables::_internal_set_enabled(bool value) {
  
  _impl_.enabled_ = value;
}
inline void FreezeAllVariables::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.FreezeAllVariables.enabled)
}

// -------------------------------------------------------------------

// QuantizationOptions

// .tensorflow.quantization.QuantizationMethod quantization_method = 1;
inline bool QuantizationOptions::_internal_has_quantization_method() const {
  return this != internal_default_instance() && _impl_.quantization_method_ != nullptr;
}
inline bool QuantizationOptions::has_quantization_method() const {
  return _internal_has_quantization_method();
}
inline void QuantizationOptions::clear_quantization_method() {
  if (GetArenaForAllocation() == nullptr && _impl_.quantization_method_ != nullptr) {
    delete _impl_.quantization_method_;
  }
  _impl_.quantization_method_ = nullptr;
}
inline const ::tensorflow::quantization::QuantizationMethod& QuantizationOptions::_internal_quantization_method() const {
  const ::tensorflow::quantization::QuantizationMethod* p = _impl_.quantization_method_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::quantization::QuantizationMethod&>(
      ::tensorflow::quantization::_QuantizationMethod_default_instance_);
}
inline const ::tensorflow::quantization::QuantizationMethod& QuantizationOptions::quantization_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.quantization_method)
  return _internal_quantization_method();
}
inline void QuantizationOptions::unsafe_arena_set_allocated_quantization_method(
    ::tensorflow::quantization::QuantizationMethod* quantization_method) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_method_);
  }
  _impl_.quantization_method_ = quantization_method;
  if (quantization_method) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.QuantizationOptions.quantization_method)
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::release_quantization_method() {
  
  ::tensorflow::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::unsafe_arena_release_quantization_method() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.QuantizationOptions.quantization_method)
  
  ::tensorflow::quantization::QuantizationMethod* temp = _impl_.quantization_method_;
  _impl_.quantization_method_ = nullptr;
  return temp;
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::_internal_mutable_quantization_method() {
  
  if (_impl_.quantization_method_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::quantization::QuantizationMethod>(GetArenaForAllocation());
    _impl_.quantization_method_ = p;
  }
  return _impl_.quantization_method_;
}
inline ::tensorflow::quantization::QuantizationMethod* QuantizationOptions::mutable_quantization_method() {
  ::tensorflow::quantization::QuantizationMethod* _msg = _internal_mutable_quantization_method();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.quantization_method)
  return _msg;
}
inline void QuantizationOptions::set_allocated_quantization_method(::tensorflow::quantization::QuantizationMethod* quantization_method) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.quantization_method_;
  }
  if (quantization_method) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(quantization_method);
    if (message_arena != submessage_arena) {
      quantization_method = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantization_method, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.quantization_method_ = quantization_method;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.QuantizationOptions.quantization_method)
}

// .tensorflow.quantization.OpSet op_set = 2;
inline void QuantizationOptions::clear_op_set() {
  _impl_.op_set_ = 0;
}
inline ::tensorflow::quantization::OpSet QuantizationOptions::_internal_op_set() const {
  return static_cast< ::tensorflow::quantization::OpSet >(_impl_.op_set_);
}
inline ::tensorflow::quantization::OpSet QuantizationOptions::op_set() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.op_set)
  return _internal_op_set();
}
inline void QuantizationOptions::_internal_set_op_set(::tensorflow::quantization::OpSet value) {
  
  _impl_.op_set_ = value;
}
inline void QuantizationOptions::set_op_set(::tensorflow::quantization::OpSet value) {
  _internal_set_op_set(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.op_set)
}

// .tensorflow.quantization.QuantizationPrecision quantization_precision = 3;
inline void QuantizationOptions::clear_quantization_precision() {
  _impl_.quantization_precision_ = 0;
}
inline ::tensorflow::quantization::QuantizationPrecision QuantizationOptions::_internal_quantization_precision() const {
  return static_cast< ::tensorflow::quantization::QuantizationPrecision >(_impl_.quantization_precision_);
}
inline ::tensorflow::quantization::QuantizationPrecision QuantizationOptions::quantization_precision() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.quantization_precision)
  return _internal_quantization_precision();
}
inline void QuantizationOptions::_internal_set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value) {
  
  _impl_.quantization_precision_ = value;
}
inline void QuantizationOptions::set_quantization_precision(::tensorflow::quantization::QuantizationPrecision value) {
  _internal_set_quantization_precision(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.quantization_precision)
}

// repeated .tensorflow.quantization.UnitWiseQuantizationPrecision unit_wise_quantization_precision = 4;
inline int QuantizationOptions::_internal_unit_wise_quantization_precision_size() const {
  return _impl_.unit_wise_quantization_precision_.size();
}
inline int QuantizationOptions::unit_wise_quantization_precision_size() const {
  return _internal_unit_wise_quantization_precision_size();
}
inline void QuantizationOptions::clear_unit_wise_quantization_precision() {
  _impl_.unit_wise_quantization_precision_.Clear();
}
inline ::tensorflow::quantization::UnitWiseQuantizationPrecision* QuantizationOptions::mutable_unit_wise_quantization_precision(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_precision)
  return _impl_.unit_wise_quantization_precision_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationPrecision >*
QuantizationOptions::mutable_unit_wise_quantization_precision() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_precision)
  return &_impl_.unit_wise_quantization_precision_;
}
inline const ::tensorflow::quantization::UnitWiseQuantizationPrecision& QuantizationOptions::_internal_unit_wise_quantization_precision(int index) const {
  return _impl_.unit_wise_quantization_precision_.Get(index);
}
inline const ::tensorflow::quantization::UnitWiseQuantizationPrecision& QuantizationOptions::unit_wise_quantization_precision(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_precision)
  return _internal_unit_wise_quantization_precision(index);
}
inline ::tensorflow::quantization::UnitWiseQuantizationPrecision* QuantizationOptions::_internal_add_unit_wise_quantization_precision() {
  return _impl_.unit_wise_quantization_precision_.Add();
}
inline ::tensorflow::quantization::UnitWiseQuantizationPrecision* QuantizationOptions::add_unit_wise_quantization_precision() {
  ::tensorflow::quantization::UnitWiseQuantizationPrecision* _add = _internal_add_unit_wise_quantization_precision();
  // @@protoc_insertion_point(field_add:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_precision)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::quantization::UnitWiseQuantizationPrecision >&
QuantizationOptions::unit_wise_quantization_precision() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.QuantizationOptions.unit_wise_quantization_precision)
  return _impl_.unit_wise_quantization_precision_;
}

// int64 min_num_elements_for_weights = 5;
inline void QuantizationOptions::clear_min_num_elements_for_weights() {
  _impl_.min_num_elements_for_weights_ = int64_t{0};
}
inline int64_t QuantizationOptions::_internal_min_num_elements_for_weights() const {
  return _impl_.min_num_elements_for_weights_;
}
inline int64_t QuantizationOptions::min_num_elements_for_weights() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.min_num_elements_for_weights)
  return _internal_min_num_elements_for_weights();
}
inline void QuantizationOptions::_internal_set_min_num_elements_for_weights(int64_t value) {
  
  _impl_.min_num_elements_for_weights_ = value;
}
inline void QuantizationOptions::set_min_num_elements_for_weights(int64_t value) {
  _internal_set_min_num_elements_for_weights(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.min_num_elements_for_weights)
}

// .tensorflow.quantization.FreezeAllVariables freeze_all_variables = 6;
inline bool QuantizationOptions::_internal_has_freeze_all_variables() const {
  return this != internal_default_instance() && _impl_.freeze_all_variables_ != nullptr;
}
inline bool QuantizationOptions::has_freeze_all_variables() const {
  return _internal_has_freeze_all_variables();
}
inline void QuantizationOptions::clear_freeze_all_variables() {
  if (GetArenaForAllocation() == nullptr && _impl_.freeze_all_variables_ != nullptr) {
    delete _impl_.freeze_all_variables_;
  }
  _impl_.freeze_all_variables_ = nullptr;
}
inline const ::tensorflow::quantization::FreezeAllVariables& QuantizationOptions::_internal_freeze_all_variables() const {
  const ::tensorflow::quantization::FreezeAllVariables* p = _impl_.freeze_all_variables_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::quantization::FreezeAllVariables&>(
      ::tensorflow::quantization::_FreezeAllVariables_default_instance_);
}
inline const ::tensorflow::quantization::FreezeAllVariables& QuantizationOptions::freeze_all_variables() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
  return _internal_freeze_all_variables();
}
inline void QuantizationOptions::unsafe_arena_set_allocated_freeze_all_variables(
    ::tensorflow::quantization::FreezeAllVariables* freeze_all_variables) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.freeze_all_variables_);
  }
  _impl_.freeze_all_variables_ = freeze_all_variables;
  if (freeze_all_variables) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
}
inline ::tensorflow::quantization::FreezeAllVariables* QuantizationOptions::release_freeze_all_variables() {
  
  ::tensorflow::quantization::FreezeAllVariables* temp = _impl_.freeze_all_variables_;
  _impl_.freeze_all_variables_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::quantization::FreezeAllVariables* QuantizationOptions::unsafe_arena_release_freeze_all_variables() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
  
  ::tensorflow::quantization::FreezeAllVariables* temp = _impl_.freeze_all_variables_;
  _impl_.freeze_all_variables_ = nullptr;
  return temp;
}
inline ::tensorflow::quantization::FreezeAllVariables* QuantizationOptions::_internal_mutable_freeze_all_variables() {
  
  if (_impl_.freeze_all_variables_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::quantization::FreezeAllVariables>(GetArenaForAllocation());
    _impl_.freeze_all_variables_ = p;
  }
  return _impl_.freeze_all_variables_;
}
inline ::tensorflow::quantization::FreezeAllVariables* QuantizationOptions::mutable_freeze_all_variables() {
  ::tensorflow::quantization::FreezeAllVariables* _msg = _internal_mutable_freeze_all_variables();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
  return _msg;
}
inline void QuantizationOptions::set_allocated_freeze_all_variables(::tensorflow::quantization::FreezeAllVariables* freeze_all_variables) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.freeze_all_variables_;
  }
  if (freeze_all_variables) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(freeze_all_variables);
    if (message_arena != submessage_arena) {
      freeze_all_variables = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, freeze_all_variables, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.freeze_all_variables_ = freeze_all_variables;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.QuantizationOptions.freeze_all_variables)
}

// bool enable_per_channel_quantization = 7;
inline void QuantizationOptions::clear_enable_per_channel_quantization() {
  _impl_.enable_per_channel_quantization_ = false;
}
inline bool QuantizationOptions::_internal_enable_per_channel_quantization() const {
  return _impl_.enable_per_channel_quantization_;
}
inline bool QuantizationOptions::enable_per_channel_quantization() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.enable_per_channel_quantization)
  return _internal_enable_per_channel_quantization();
}
inline void QuantizationOptions::_internal_set_enable_per_channel_quantization(bool value) {
  
  _impl_.enable_per_channel_quantization_ = value;
}
inline void QuantizationOptions::set_enable_per_channel_quantization(bool value) {
  _internal_set_enable_per_channel_quantization(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.enable_per_channel_quantization)
}

// bool enable_two_input_tensors = 8;
inline void QuantizationOptions::clear_enable_two_input_tensors() {
  _impl_.enable_two_input_tensors_ = false;
}
inline bool QuantizationOptions::_internal_enable_two_input_tensors() const {
  return _impl_.enable_two_input_tensors_;
}
inline bool QuantizationOptions::enable_two_input_tensors() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.QuantizationOptions.enable_two_input_tensors)
  return _internal_enable_two_input_tensors();
}
inline void QuantizationOptions::_internal_set_enable_two_input_tensors(bool value) {
  
  _impl_.enable_two_input_tensors_ = value;
}
inline void QuantizationOptions::set_enable_two_input_tensors(bool value) {
  _internal_set_enable_two_input_tensors(value);
  // @@protoc_insertion_point(field_set:tensorflow.quantization.QuantizationOptions.enable_two_input_tensors)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace quantization
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::quantization::QuantizationMethod_Method> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::QuantizationMethod_Method>() {
  return ::tensorflow::quantization::QuantizationMethod_Method_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod>() {
  return ::tensorflow::quantization::QuantizationMethod_ExperimentalMethod_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType>() {
  return ::tensorflow::quantization::UnitWiseQuantizationPrecision_UnitType_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::QuantizationPrecision> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::QuantizationPrecision>() {
  return ::tensorflow::quantization::QuantizationPrecision_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::quantization::OpSet> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::quantization::OpSet>() {
  return ::tensorflow::quantization::OpSet_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fquantization_5foptions_2eproto
